<template>
  <div class="flex-column">
    <div class="absolute inset-0 flex-column overflow-auto">
      <div class="absolute inset-0 flex-column overflow-auto">
        <!-- Fixed Header -->
      <div class="flex-shrink-0 bg-gray-800 shadow-lg border-b border-gray-700 h-10">
        <div class="flex-1 flex">
          <button v-for="tab in state.tabs" :key="tab.key" @click="state.activeTab = tab.key, $router.push(tab.key)" :class="[
            'px-4 py-2 text-sm font-medium transition-colors duration-200 border-b-2',
            state.activeTab === tab.key
              ? 'text-blue-400 border-blue-400 bg-gray-800'
              : 'text-gray-400 border-transparent hover:text-gray-200 hover:bg-gray-700'
          ]">
            <div class="flex items-center gap-2">
              <component :is="tab.icon" class="w-4 h-4" />
              <span>{{ tab.label }}</span>
            </div>
          </button>
        </div>
      </div>
        <header class="flex-shrink-0 bg-gray-800 shadow-lg border-b border-gray-700">
          <div class="flex items-center h-16 px-4">
            <Progress />
            <div class="flex-1"></div>
            <ConsoleLog />
          </div>
        </header>

        <!-- Main Content Area - Fixed Height -->
        <main class="flex-column flex-1">
          <!-- Tabs Container -->
          <div class="flex-column flex-1">
            <a-tabs v-model:activeKey="ttsStore.activeTab" :tab-bar-style="tabBarStyle" class="flex-column flex-1">
              <a-tab-pane key="tts" tab="Text to Speech" :tab-bar-style="tabBarStyle" class="flex-column flex-1">
                <div class="flex-column flex-1">
                  <TextToSpeech />
                </div>
              </a-tab-pane>

              <a-tab-pane key="srt" tab="Subtitle Translator" class="!flex-column !flex-1">
                <div class="flex-column flex-1">
                  <!-- <PanelPage /> -->
                  <SubtitleTranslatorTerm />
                </div>
              </a-tab-pane>

              <a-tab-pane key="srt-table" tab="SRT Table" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <SrtTableProcessor />
                </div>
              </a-tab-pane>

              <a-tab-pane key="history" tab="Generated Audio" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <AudioList />
                </div>
              </a-tab-pane>

              <a-tab-pane key="video-speed" tab="Video Speed" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <VideoSpeedAdjuster />
                </div>
              </a-tab-pane>

              <a-tab-pane key="whisper" tab="Whisper Transcription" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <WhisperProcessor />
                </div>
              </a-tab-pane>

              <a-tab-pane key="video-ocr" tab="Video OCR" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <VideoOcrProcessor />
                </div>
              </a-tab-pane>

              <a-tab-pane key="video-cutter" tab="Video Cutter" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <VideoCutter />
                </div>
              </a-tab-pane>

              <a-tab-pane key="video-renderer" tab="SRT Video Renderer" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <SrtVideoRenderer />
                </div>
              </a-tab-pane>

              <a-tab-pane key="config" tab="Config" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <CookieInput />
                </div>
              </a-tab-pane>

              <a-tab-pane key="layout-test" tab="Layout Test" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <LayoutTestComponent />
                </div>
              </a-tab-pane>

              <a-tab-pane key="panel-test" tab="Panel Test" class="flex-1 min-h-0">
                <div class="flex-1 min-h-0">
                  <PanelPageTest />
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </main>
      </div>
    </div>

  </div>
</template>

<script setup>
import { useTTSStore } from '@/stores/ttsStore';
import { state } from '@/lib/state';

const ttsStore = useTTSStore();
</script>
