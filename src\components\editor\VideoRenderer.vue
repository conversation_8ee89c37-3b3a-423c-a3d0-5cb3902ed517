<template>
  <a-modal
    :open="visible"
    title="Render Video"
    width="600px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="video-renderer space-y-4">
      <!-- Render Settings -->
      <div class="render-settings bg-gray-50 dark:bg-gray-800 rounded p-4">
        <h4 class="text-sm font-medium mb-3">Render Settings</h4>
        
        <div class="grid grid-cols-2 gap-4">
          <!-- Resolution -->
          <div>
            <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1">Resolution</label>
            <a-select v-model:value="settings.resolution" size="small" class="w-full">
              <a-select-option value="1920x1080">1920x1080 (1080p)</a-select-option>
              <a-select-option value="1280x720">1280x720 (720p)</a-select-option>
              <a-select-option value="3840x2160">3840x2160 (4K)</a-select-option>
            </a-select>
          </div>
          
          <!-- FPS -->
          <div>
            <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1">Frame Rate</label>
            <a-select v-model:value="settings.fps" size="small" class="w-full">
              <a-select-option :value="24">24 fps</a-select-option>
              <a-select-option :value="30">30 fps</a-select-option>
              <a-select-option :value="60">60 fps</a-select-option>
            </a-select>
          </div>
          
          <!-- Bitrate -->
          <div>
            <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1">Bitrate</label>
            <a-slider
              v-model:value="settings.bitrate"
              :min="1000"
              :max="20000"
              :step="500"
              size="small"
            />
            <div class="text-xs text-gray-500 mt-1">{{ settings.bitrate }}kbps</div>
          </div>
          
          <!-- Codec -->
          <div>
            <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1">Codec</label>
            <a-select v-model:value="settings.codec" size="small" class="w-full">
              <a-select-option value="h264">H.264</a-select-option>
              <a-select-option value="h265">H.265 (HEVC)</a-select-option>
              <a-select-option value="vp9">VP9</a-select-option>
            </a-select>
          </div>
        </div>
        
        <!-- Output Path -->
        <div class="mt-4">
          <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1">Output Path</label>
          <div class="flex gap-2">
            <a-input
              v-model:value="settings.outputPath"
              placeholder="Select output path..."
              size="small"
              readonly
            />
            <a-button size="small" @click="selectOutputPath">Browse</a-button>
          </div>
        </div>
      </div>
      
      <!-- Layers Preview -->
      <div class="layers-preview bg-gray-50 dark:bg-gray-800 rounded p-4">
        <h4 class="text-sm font-medium mb-3">Layers to Render</h4>
        
        <div class="space-y-2 max-h-40 overflow-auto">
          <div
            v-for="layer in enabledLayers"
            :key="layer.id"
            class="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded"
          >
            <div class="flex items-center gap-2">
              <div class="layer-icon">
                <svg v-if="layer.type === 'video'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="23 7 16 12 23 17 23 7"/>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                </svg>
                <svg v-else-if="layer.type === 'subtitle'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                <svg v-else-if="layer.type === 'text'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="4,7 4,4 20,4 20,7"/>
                  <line x1="9" y1="20" x2="15" y2="20"/>
                  <line x1="12" y1="4" x2="12" y2="20"/>
                </svg>
                <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                </svg>
              </div>
              <span class="text-sm">{{ layer.name }}</span>
            </div>
            <span class="text-xs text-gray-500">{{ layer.opacity }}%</span>
          </div>
        </div>
      </div>
      
      <!-- Effects Preview -->
      <div v-if="activeEffects.length > 0" class="effects-preview bg-gray-50 dark:bg-gray-800 rounded p-4">
        <h4 class="text-sm font-medium mb-3">Effects to Apply</h4>
        
        <div class="space-y-2 max-h-32 overflow-auto">
          <div
            v-for="effect in activeEffects"
            :key="effect.id"
            class="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded"
          >
            <div class="flex items-center gap-2">
              <span class="text-sm capitalize">{{ effect.type }}</span>
            </div>
            <span class="text-xs text-gray-500">
              {{ formatTime(effect.timeStart) }} - {{ formatTime(effect.timeEnd) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Progress -->
      <div v-if="isRendering" class="render-progress bg-gray-50 dark:bg-gray-800 rounded p-4">
        <h4 class="text-sm font-medium mb-3">Rendering Progress</h4>
        
        <a-progress
          :percent="renderProgress"
          :status="renderStatus"
          :format="(percent) => `${percent}% - ${currentStep}`"
        />
        
        <div class="mt-2 text-xs text-gray-600 dark:text-gray-400">
          {{ renderLog }}
        </div>
        
        <div v-if="renderError" class="mt-2 text-xs text-red-600">
          Error: {{ renderError }}
        </div>
      </div>
      
      <!-- Actions -->
      <div class="flex justify-end gap-2">
        <a-button @click="handleCancel" :disabled="isRendering">
          Cancel
        </a-button>
        <a-button
          type="primary"
          @click="startRender"
          :loading="isRendering"
          :disabled="!settings.outputPath"
        >
          {{ isRendering ? 'Rendering...' : 'Start Render' }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { useTTSStore } from '@/stores/ttsStore'
import { message } from 'ant-design-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'render-complete'])

const layersStore = useVideoLayersStore()
const ttsStore = useTTSStore()

// Local state
const settings = ref({
  resolution: '1920x1080',
  fps: 30,
  bitrate: 5000,
  codec: 'h264',
  outputPath: ''
})

const isRendering = ref(false)
const renderProgress = ref(0)
const renderStatus = ref('normal')
const currentStep = ref('')
const renderLog = ref('')
const renderError = ref('')

// Computed
const enabledLayers = computed(() => layersStore.enabledLayers)
const activeEffects = computed(() => {
  const effects = layersStore.activeEffects
  return [
    ...effects.delogo.map(e => ({ ...e, type: 'delogo' })),
    ...effects.blur.map(e => ({ ...e, type: 'blur' })),
    ...effects.text.map(e => ({ ...e, type: 'text' })),
    ...effects.image.map(e => ({ ...e, type: 'image' }))
  ]
})

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const selectOutputPath = async () => {
  try {
    // Use Electron's dialog API if available
    if (window.electronAPI) {
      const result = await window.electronAPI.showSaveDialog({
        filters: [
          { name: 'Video Files', extensions: ['mp4', 'mov', 'avi'] }
        ],
        defaultPath: 'rendered_video.mp4'
      })
      
      if (!result.canceled) {
        settings.value.outputPath = result.filePath
      }
    } else {
      // Fallback for web
      settings.value.outputPath = 'rendered_video.mp4'
      message.info('Please specify output path manually')
    }
  } catch (error) {
    console.error('Error selecting output path:', error)
    message.error('Failed to select output path')
  }
}

const generateFFmpegCommand = () => {
  const videoLayer = layersStore.getLayerById('video-bg')
  if (!videoLayer || !videoLayer.properties.src) {
    throw new Error('No video source found')
  }
  
  const [width, height] = settings.value.resolution.split('x').map(Number)
  
  let command = [
    'ffmpeg',
    '-i', videoLayer.properties.src,
    '-vf'
  ]
  
  let filters = []
  
  // Video filters
  filters.push(`scale=${width}:${height}`)
  
  // Apply effects
  activeEffects.value.forEach(effect => {
    switch (effect.type) {
      case 'blur':
        const blurX = Math.round(effect.x * width)
        const blurY = Math.round(effect.y * height)
        const blurW = Math.round(effect.width * width)
        const blurH = Math.round(effect.height * height)
        filters.push(`boxblur=enable='between(t,${effect.timeStart},${effect.timeEnd})':x=${blurX}:y=${blurY}:w=${blurW}:h=${blurH}`)
        break
        
      case 'delogo':
        const logoX = Math.round(effect.x * width)
        const logoY = Math.round(effect.y * height)
        const logoW = Math.round(effect.width * width)
        const logoH = Math.round(effect.height * height)
        filters.push(`delogo=enable='between(t,${effect.timeStart},${effect.timeEnd})':x=${logoX}:y=${logoY}:w=${logoW}:h=${logoH}`)
        break
    }
  })
  
  // Add subtitle overlay if enabled
  const subtitleLayer = layersStore.getLayerById('subtitles')
  if (subtitleLayer && subtitleLayer.enabled && ttsStore.currentSrtList) {
    // Generate ASS subtitle file
    const assContent = generateASSSubtitles()
    filters.push(`ass=${assContent}`)
  }
  
  command.push(filters.join(','))
  
  // Audio settings
  command.push('-c:a', 'aac', '-b:a', '128k')
  
  // Video settings
  command.push('-c:v', settings.value.codec)
  command.push('-b:v', `${settings.value.bitrate}k`)
  command.push('-r', settings.value.fps.toString())
  
  // Output
  command.push('-y', settings.value.outputPath)
  
  return command
}

const generateASSSubtitles = () => {
  const subtitleLayer = layersStore.getLayerById('subtitles')
  const items = ttsStore.currentSrtList.items
  
  if (!subtitleLayer || !items) return ''
  
  const props = subtitleLayer.properties
  const [width, height] = settings.value.resolution.split('x').map(Number)
  
  let ass = `[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+
PlayResX: ${width}
PlayResY: ${height}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${props.fontFamily || 'Arial'},${props.fontSize || 48},&H${colorToASS(props.color)},&H${colorToASS(props.color)},&H${colorToASS(props.borderColor)},&H${colorToASS(props.backgroundColor)},${props.bold ? -1 : 0},0,0,0,100,100,0,0,1,${props.borderWidth || 2},0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`
  
  items.forEach(item => {
    const start = formatASSTime(item.startTime || parseTimeToSeconds(item.start))
    const end = formatASSTime(item.endTime || parseTimeToSeconds(item.end))
    const text = item.translatedText || item.text || ''
    
    ass += `Dialogue: 0,${start},${end},Default,,0,0,0,,${text}\n`
  })
  
  return ass
}

const colorToASS = (color) => {
  if (!color) return '00FFFFFF'
  
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  return `00${b.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${r.toString(16).padStart(2, '0')}`.toUpperCase()
}

const formatASSTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toFixed(2).padStart(5, '0')}`
}

const parseTimeToSeconds = (timeString) => {
  const [time, ms] = timeString.split(',')
  const [hours, minutes, seconds] = time.split(':').map(Number)
  return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000
}

const startRender = async () => {
  try {
    isRendering.value = true
    renderProgress.value = 0
    renderStatus.value = 'active'
    renderError.value = ''
    currentStep.value = 'Preparing...'
    
    // Generate FFmpeg command
    const command = generateFFmpegCommand()
    renderLog.value = `Command: ${command.join(' ')}`
    
    currentStep.value = 'Rendering video...'
    
    // Simulate rendering progress
    const progressInterval = setInterval(() => {
      if (renderProgress.value < 90) {
        renderProgress.value += Math.random() * 10
        currentStep.value = `Processing frame ${Math.floor(renderProgress.value * 30)}/3000`
      }
    }, 500)
    
    // TODO: Execute actual FFmpeg command
    // This would need to be implemented with Node.js backend or Electron
    
    // Simulate completion
    setTimeout(() => {
      clearInterval(progressInterval)
      renderProgress.value = 100
      renderStatus.value = 'success'
      currentStep.value = 'Complete!'
      isRendering.value = false
      
      message.success('Video rendered successfully!')
      emit('render-complete', settings.value.outputPath)
    }, 5000)
    
  } catch (error) {
    console.error('Render error:', error)
    renderError.value = error.message
    renderStatus.value = 'exception'
    isRendering.value = false
    message.error('Render failed: ' + error.message)
  }
}

const handleCancel = () => {
  if (isRendering.value) {
    // TODO: Cancel rendering process
    isRendering.value = false
    renderStatus.value = 'normal'
    renderProgress.value = 0
  }
  
  emit('update:visible', false)
}

// Watch for visibility changes
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // Reset state when modal opens
    renderProgress.value = 0
    renderStatus.value = 'normal'
    renderError.value = ''
    currentStep.value = ''
    renderLog.value = ''
  }
})
</script>

<style scoped>
.video-renderer :deep(.ant-slider) {
  margin: 0;
}

.video-renderer :deep(.ant-select) {
  width: 100%;
}

.layer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
