# Video Editor Guide - VideoPrevivewEditor

## Tổng quan

VideoPrevivewEditor đã đượ<PERSON> phát triển thành một video editor chuy<PERSON><PERSON> nghiệp tương tự như CapCut, với đầy đủ các tính năng layers, effects, và rendering. Component này được tích hợp trong HomePage.vue và cung cấp trải nghiệm chỉnh sửa video hoàn chỉnh.

## Cấu trúc Component

### 🎬 **VideoPrevivewEditor.vue** (Component chính)
- Video preview với overlay system
- Tab-based interface (Preview, Effects, Layers)
- Real-time subtitle rendering
- Professional video editing controls

### 🎨 **Overlay Components**
1. **SubtitleOverlay.vue** - Hiển thị subtitle với voice-specific styling
2. **TextOverlay.vue** - Text overlays với animations
3. **ImageOverlay.vue** - Image/logo overlays với transforms
4. **EffectOverlay.vue** - Visual effects (blur, delogo, crop)
5. **GridOverlay.vue** - Rule of thirds grid
6. **SafeAreaOverlay.vue** - Safe area boundaries

### 🎛️ **Control Panels**
1. **VideoLayersPanel.vue** - Quản lý layers như Photoshop
2. **EffectsPanel.vue** - Effects controls và settings
3. **VideoRenderer.vue** - FFmpeg rendering modal

### 🗄️ **Data Management**
- **video-layers-store.js** - Pinia store quản lý layers và effects

## Tính năng chính

### 🎥 **Video Preview**
- ✅ Real-time preview với overlays
- ✅ Zoom controls (50%, 75%, 100%, 125%)
- ✅ Quality settings (Low, Medium, High)
- ✅ Grid và Safe Area overlays
- ✅ Click-to-position functionality

### 📝 **Subtitle System**
- ✅ Voice-specific subtitle styling
- ✅ Real-time subtitle overlay
- ✅ Position và color customization
- ✅ Font size và family controls
- ✅ Shadow và border effects
- ✅ ASS subtitle format support

### 🎨 **Layers System**
- ✅ Layer-based editing như Photoshop
- ✅ Drag & drop reordering
- ✅ Opacity và blend mode controls
- ✅ Lock/unlock layers
- ✅ Show/hide toggles
- ✅ Layer properties panel

### ⚡ **Effects System**
- ✅ **Blur Effect** - Blur specific areas
- ✅ **Delogo Effect** - Remove logos/watermarks
- ✅ **Color Correction** - Brightness, contrast, saturation
- ✅ **Crop Effect** - Crop video areas
- ✅ **Text Animations** - Fade, slide, typewriter, bounce
- ✅ Time-based effect application

### 🖼️ **Media Overlays**
- ✅ **Text Overlays** - Custom text với animations
- ✅ **Image Overlays** - Logos, watermarks, graphics
- ✅ **Position Controls** - Drag to position
- ✅ **Transform Controls** - Scale, rotate, opacity
- ✅ **Animation System** - Multiple animation types

### 🎬 **Rendering System**
- ✅ **FFmpeg Integration** - Professional video rendering
- ✅ **Multiple Resolutions** - 720p, 1080p, 4K
- ✅ **Codec Options** - H.264, H.265, VP9
- ✅ **Bitrate Control** - Quality settings
- ✅ **Progress Tracking** - Real-time render progress

## Cách sử dụng

### 1. **Basic Setup**
```vue
<!-- In HomePage.vue -->
<VideoPrevivewEditor />
```

### 2. **Tab Interface**
- **Preview Tab**: Basic controls và progress tracking
- **Effects Tab**: Add và configure effects
- **Layers Tab**: Manage layers như Photoshop

### 3. **Adding Effects**
```javascript
// Add blur effect
layersStore.addEffect('blur', {
  x: 0.1,      // 10% from left
  y: 0.1,      // 10% from top  
  width: 0.2,  // 20% width
  height: 0.2, // 20% height
  intensity: 10
})

// Add text overlay
layersStore.addLayer({
  type: 'text',
  name: 'Title Text',
  properties: {
    text: 'My Title',
    fontSize: 48,
    color: '#ffffff',
    position: { x: 50, y: 20 },
    animation: { type: 'fade', duration: 1.0 }
  }
})
```

### 4. **Subtitle Customization**
```javascript
// Update subtitle settings
layersStore.updateLayerProperty('subtitles', 'properties.fontSize', 36)
layersStore.updateLayerProperty('subtitles', 'properties.color', '#ffff00')
layersStore.updateLayerProperty('subtitles', 'properties.position.y', 85)
```

### 5. **Rendering Video**
```javascript
// Open render modal
renderVideo()

// Configure render settings
{
  resolution: '1920x1080',
  fps: 30,
  bitrate: 5000,
  codec: 'h264',
  outputPath: 'output.mp4'
}
```

## Voice-Specific Subtitle Styling

Hệ thống tự động áp dụng styling dựa trên voice configuration:

```javascript
// Voice config từ subtitleStore.renderVoiceOptions
const voiceConfig = {
  isVoice1: {
    subtitleFontSize: 48,
    subtitleTextColor: '#ffffff',
    subtitleBackgroundColor: '#000000',
    subtitleBorderColor: '#333333',
    subtitleBold: true,
    shadowSize: 3,
    assOptions: {
      posX: 50,
      posY: 85,
      align: 2  // 1=left, 2=center, 3=right
    }
  }
}
```

## Coordinate System

### Video Coordinates
- **Position**: Percentage-based (0-100%)
- **X**: 0 = left edge, 100 = right edge
- **Y**: 0 = top edge, 100 = bottom edge

### Effect Areas
- **Normalized**: 0.0 - 1.0 (recommended)
- **Pixel**: Absolute pixel values
- **Auto-scaling**: Adapts to video resolution

## Animation System

### Text Animations
```javascript
const animations = {
  fade: {
    type: 'fade',
    duration: 0.5,
    easing: 'ease-in-out'
  },
  slide: {
    type: 'slide', 
    duration: 1.0,
    direction: 'left'
  },
  typewriter: {
    type: 'typewriter',
    duration: 2.0,
    speed: 'normal'
  },
  bounce: {
    type: 'bounce',
    duration: 0.8,
    intensity: 'medium'
  }
}
```

## FFmpeg Integration

### Command Generation
```bash
ffmpeg -i input.mp4 \
  -vf "scale=1920:1080,boxblur=enable='between(t,10,15)':x=100:y=100:w=200:h=100,ass=subtitles.ass" \
  -c:v h264 -b:v 5000k -r 30 \
  -c:a aac -b:a 128k \
  -y output.mp4
```

### Supported Effects
- **boxblur**: Blur specific areas
- **delogo**: Remove logos
- **ass**: Advanced subtitle rendering
- **scale**: Resolution scaling
- **crop**: Video cropping

## Performance Optimization

### 1. **Layer Virtualization**
```javascript
// Only render visible layers
const visibleLayers = computed(() => 
  layers.filter(layer => 
    layer.enabled && 
    isInTimeRange(layer, currentTime.value)
  )
)
```

### 2. **Effect Caching**
```javascript
// Cache effect calculations
const effectCache = new Map()
const getCachedEffect = (effectId, time) => {
  const key = `${effectId}-${time}`
  if (!effectCache.has(key)) {
    effectCache.set(key, calculateEffect(effectId, time))
  }
  return effectCache.get(key)
}
```

### 3. **Debounced Updates**
```javascript
import { debounce } from 'lodash-es'

const debouncedUpdate = debounce((property, value) => {
  layersStore.updateLayerProperty(layerId, property, value)
}, 100)
```

## Integration với Timeline

```javascript
// Sync với timeline store
watch(() => timelineStore.currentTime, (newTime) => {
  layersStore.setCurrentTime(newTime)
})

// Update layers từ timeline
watch(() => timelineStore.selectedItems, (selectedIds) => {
  // Highlight corresponding layers
})
```

## Troubleshooting

### Common Issues

1. **Overlay không hiển thị**
   - Check layer enabled status
   - Verify time range
   - Check z-index conflicts

2. **Performance lag**
   - Reduce preview quality
   - Disable unnecessary overlays
   - Use layer virtualization

3. **Render fails**
   - Check FFmpeg installation
   - Verify file paths
   - Check codec support

### Debug Tools

```javascript
// Layer debugging
console.log('Active layers:', layersStore.enabledLayers)
console.log('Current effects:', layersStore.activeEffects)

// Performance monitoring
const startTime = performance.now()
// ... operation
console.log('Operation took:', performance.now() - startTime, 'ms')
```

## Roadmap

### Phase 1 (Completed)
- ✅ Basic video preview
- ✅ Subtitle overlay system
- ✅ Layers management
- ✅ Effects system
- ✅ Rendering pipeline

### Phase 2 (Planned)
- 📋 Keyframe animation system
- 📋 Audio waveform display
- 📋 Video thumbnails timeline
- 📋 Advanced color grading
- 📋 Motion tracking

### Phase 3 (Future)
- 📋 3D transforms
- 📋 Particle effects
- 📋 Green screen (chroma key)
- 📋 Multi-track editing
- 📋 Real-time collaboration

Video editor này cung cấp trải nghiệm chỉnh sửa video chuyên nghiệp tương đương với CapCut, với khả năng mở rộng cao cho các tính năng tương lai!
