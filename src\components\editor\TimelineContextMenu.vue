<template>
  <div
    v-if="visible"
    class="timeline-context-menu fixed bg-gray-800 border border-gray-600 rounded-lg shadow-lg py-1 z-50 min-w-[180px]"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @click.stop
  >
    <!-- Single item actions -->
    <template v-if="selectedItems.length === 1">
      <div class="px-3 py-1 text-xs text-gray-400 border-b border-gray-600">
        Subtitle {{ selectedItems[0] }}
      </div>

      <button
        @click="editSubtitle"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
        Edit Text
      </button>

      <button
        @click="splitSubtitle"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M16 3h5v5"/>
          <path d="M8 3H3v5"/>
          <path d="M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3"/>
          <path d="M12 22v-8.3a4 4 0 0 1 1.172-2.872L21 3"/>
        </svg>
        Split Subtitle
      </button>

      <button
        @click="duplicateSubtitle"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
        </svg>
        Duplicate
      </button>

      <div class="border-t border-gray-600 my-1"></div>

      <button
        @click="generateAudio"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
        Generate Audio
      </button>
    </template>

    <!-- Multiple items actions -->
    <template v-else-if="selectedItems.length > 1">
      <div class="px-3 py-1 text-xs text-gray-400 border-b border-gray-600">
        {{ selectedItems.length }} subtitles selected
      </div>

      <button
        @click="mergeSubtitles"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M8 3H3v5"/>
          <path d="M16 3h5v5"/>
          <path d="M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3"/>
          <path d="M12 22v-8.3a4 4 0 0 1 1.172-2.872L21 3"/>
        </svg>
        Merge Subtitles
      </button>

      <button
        @click="alignSubtitles"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="21" y1="10" x2="3" y2="10"/>
          <line x1="21" y1="6" x2="3" y2="6"/>
          <line x1="21" y1="14" x2="3" y2="14"/>
          <line x1="21" y1="18" x2="3" y2="18"/>
        </svg>
        Align Timing
      </button>

      <button
        @click="generateAudioBatch"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
        Generate Audio (Batch)
      </button>
    </template>

    <!-- No selection actions -->
    <template v-else>
      <button
        @click="insertSubtitle"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        Insert Subtitle Here
      </button>

      <button
        @click="pasteSubtitle"
        :disabled="!hasClipboard"
        class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
        </svg>
        Paste
      </button>
    </template>

    <!-- Common actions -->
    <div class="border-t border-gray-600 my-1"></div>

    <button
      v-if="selectedItems.length > 0"
      @click="copySubtitles"
      class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center gap-2"
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
      </svg>
      Copy
    </button>

    <button
      v-if="selectedItems.length > 0"
      @click="deleteSubtitles"
      class="w-full text-left px-3 py-2 text-sm text-red-400 hover:bg-red-900 hover:bg-opacity-30 flex items-center gap-2"
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="3,6 5,6 21,6"/>
        <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
        <line x1="10" y1="11" x2="10" y2="17"/>
        <line x1="14" y1="11" x2="14" y2="17"/>
      </svg>
      Delete
    </button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  clickTime: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['close', 'action'])

const timelineStore = useTimelineStore()

// Computed
const selectedItems = computed(() => timelineStore.selectedItems)
const hasClipboard = ref(false) // TODO: Implement clipboard detection

// Methods
const editSubtitle = () => {
  emit('action', 'edit', selectedItems.value[0])
  emit('close')
}

const splitSubtitle = () => {
  emit('action', 'split', selectedItems.value[0])
  emit('close')
}

const duplicateSubtitle = () => {
  emit('action', 'duplicate', selectedItems.value[0])
  emit('close')
}

const mergeSubtitles = () => {
  emit('action', 'merge', selectedItems.value)
  emit('close')
}

const alignSubtitles = () => {
  emit('action', 'align', selectedItems.value)
  emit('close')
}

const generateAudio = () => {
  emit('action', 'generate-audio', selectedItems.value[0])
  emit('close')
}

const generateAudioBatch = () => {
  emit('action', 'generate-audio-batch', selectedItems.value)
  emit('close')
}

const insertSubtitle = () => {
  emit('action', 'insert', props.clickTime)
  emit('close')
}

const copySubtitles = () => {
  emit('action', 'copy', selectedItems.value)
  emit('close')
}

const pasteSubtitle = () => {
  emit('action', 'paste', props.clickTime)
  emit('close')
}

const deleteSubtitles = () => {
  emit('action', 'delete', selectedItems.value)
  emit('close')
}
</script>

<style scoped>
.timeline-context-menu {
  animation: fadeIn 0.1s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
