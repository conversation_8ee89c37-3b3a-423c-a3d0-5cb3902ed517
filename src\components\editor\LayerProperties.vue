<template>
  <div class="layer-properties space-y-3">
    <!-- Common Properties -->
    <div class="space-y-2">
      <!-- Opacity -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Opacity</label>
        <a-slider
          :value="layer.opacity"
          @change="(value) => $emit('update', 'opacity', value)"
          :min="0"
          :max="100"
          :step="1"
          size="small"
        />
      </div>
      
      <!-- Blend Mode -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Blend Mode</label>
        <a-select
          :value="layer.blendMode"
          @change="(value) => $emit('update', 'blendMode', value)"
          size="small"
          class="w-full"
        >
          <a-select-option value="normal">Normal</a-select-option>
          <a-select-option value="multiply">Multiply</a-select-option>
          <a-select-option value="screen">Screen</a-select-option>
          <a-select-option value="overlay">Overlay</a-select-option>
          <a-select-option value="soft-light">Soft Light</a-select-option>
          <a-select-option value="hard-light">Hard Light</a-select-option>
        </a-select>
      </div>
    </div>
    
    <!-- Subtitle Properties -->
    <div v-if="layer.type === 'subtitle'" class="space-y-2">
      <h5 class="text-xs font-medium text-gray-300 border-b border-gray-600 pb-1">SUBTITLE SETTINGS</h5>
      
      <!-- Font Size -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Font Size</label>
        <a-input-number
          :value="layer.properties.fontSize"
          @change="(value) => $emit('update', 'properties.fontSize', value)"
          :min="12"
          :max="200"
          size="small"
          class="w-full"
        />
      </div>
      
      <!-- Font Family -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Font Family</label>
        <a-select
          :value="layer.properties.fontFamily"
          @change="(value) => $emit('update', 'properties.fontFamily', value)"
          size="small"
          class="w-full"
        >
          <a-select-option value="Arial">Arial</a-select-option>
          <a-select-option value="Helvetica">Helvetica</a-select-option>
          <a-select-option value="Times New Roman">Times New Roman</a-select-option>
          <a-select-option value="Courier New">Courier New</a-select-option>
          <a-select-option value="Verdana">Verdana</a-select-option>
        </a-select>
      </div>
      
      <!-- Colors -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <label class="text-xs text-gray-400 block mb-1">Text Color</label>
          <input
            type="color"
            :value="layer.properties.color"
            @input="(e) => $emit('update', 'properties.color', e.target.value)"
            class="w-full h-8 rounded border border-gray-600"
          />
        </div>
        <div>
          <label class="text-xs text-gray-400 block mb-1">Background</label>
          <input
            type="color"
            :value="layer.properties.backgroundColor"
            @input="(e) => $emit('update', 'properties.backgroundColor', e.target.value)"
            class="w-full h-8 rounded border border-gray-600"
          />
        </div>
      </div>
      
      <!-- Border -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <label class="text-xs text-gray-400 block mb-1">Border Color</label>
          <input
            type="color"
            :value="layer.properties.borderColor"
            @input="(e) => $emit('update', 'properties.borderColor', e.target.value)"
            class="w-full h-8 rounded border border-gray-600"
          />
        </div>
        <div>
          <label class="text-xs text-gray-400 block mb-1">Border Width</label>
          <a-input-number
            :value="layer.properties.borderWidth"
            @change="(value) => $emit('update', 'properties.borderWidth', value)"
            :min="0"
            :max="10"
            size="small"
            class="w-full"
          />
        </div>
      </div>
      
      <!-- Text Style -->
      <div class="flex gap-2">
        <a-checkbox
          :checked="layer.properties.bold"
          @change="(e) => $emit('update', 'properties.bold', e.target.checked)"
        >
          <span class="text-xs">Bold</span>
        </a-checkbox>
        <a-checkbox
          :checked="layer.properties.italic"
          @change="(e) => $emit('update', 'properties.italic', e.target.checked)"
        >
          <span class="text-xs">Italic</span>
        </a-checkbox>
        <a-checkbox
          :checked="layer.properties.underline"
          @change="(e) => $emit('update', 'properties.underline', e.target.checked)"
        >
          <span class="text-xs">Underline</span>
        </a-checkbox>
      </div>
      
      <!-- Alignment -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Alignment</label>
        <a-radio-group
          :value="layer.properties.alignment"
          @change="(e) => $emit('update', 'properties.alignment', e.target.value)"
          size="small"
        >
          <a-radio-button value="left">Left</a-radio-button>
          <a-radio-button value="center">Center</a-radio-button>
          <a-radio-button value="right">Right</a-radio-button>
        </a-radio-group>
      </div>
      
      <!-- Position -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <label class="text-xs text-gray-400 block mb-1">X Position (%)</label>
          <a-slider
            :value="layer.properties.position.x"
            @change="(value) => $emit('update', 'properties.position.x', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
        <div>
          <label class="text-xs text-gray-400 block mb-1">Y Position (%)</label>
          <a-slider
            :value="layer.properties.position.y"
            @change="(value) => $emit('update', 'properties.position.y', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
      </div>
      
      <!-- Shadow -->
      <div v-if="layer.properties.shadow" class="space-y-2">
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Shadow</label>
          <a-switch
            :checked="layer.properties.shadow.enabled"
            @change="(checked) => $emit('update', 'properties.shadow.enabled', checked)"
            size="small"
          />
        </div>
        
        <div v-if="layer.properties.shadow.enabled" class="space-y-2 pl-2">
          <div>
            <label class="text-xs text-gray-400 block mb-1">Shadow Blur</label>
            <a-slider
              :value="layer.properties.shadow.blur"
              @change="(value) => $emit('update', 'properties.shadow.blur', value)"
              :min="0"
              :max="20"
              size="small"
            />
          </div>
          
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="text-xs text-gray-400 block mb-1">Offset X</label>
              <a-input-number
                :value="layer.properties.shadow.offsetX"
                @change="(value) => $emit('update', 'properties.shadow.offsetX', value)"
                :min="-20"
                :max="20"
                size="small"
                class="w-full"
              />
            </div>
            <div>
              <label class="text-xs text-gray-400 block mb-1">Offset Y</label>
              <a-input-number
                :value="layer.properties.shadow.offsetY"
                @change="(value) => $emit('update', 'properties.shadow.offsetY', value)"
                :min="-20"
                :max="20"
                size="small"
                class="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Text Layer Properties -->
    <div v-else-if="layer.type === 'text'" class="space-y-2">
      <h5 class="text-xs font-medium text-gray-300 border-b border-gray-600 pb-1">TEXT SETTINGS</h5>
      
      <!-- Text Content -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Text</label>
        <a-textarea
          :value="layer.properties.text"
          @change="(e) => $emit('update', 'properties.text', e.target.value)"
          :rows="2"
          size="small"
        />
      </div>
      
      <!-- Font Size -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Font Size</label>
        <a-input-number
          :value="layer.properties.fontSize"
          @change="(value) => $emit('update', 'properties.fontSize', value)"
          :min="12"
          :max="200"
          size="small"
          class="w-full"
        />
      </div>
      
      <!-- Color -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Color</label>
        <input
          type="color"
          :value="layer.properties.color"
          @input="(e) => $emit('update', 'properties.color', e.target.value)"
          class="w-full h-8 rounded border border-gray-600"
        />
      </div>
      
      <!-- Position -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <label class="text-xs text-gray-400 block mb-1">X Position (%)</label>
          <a-slider
            :value="layer.properties.position.x"
            @change="(value) => $emit('update', 'properties.position.x', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
        <div>
          <label class="text-xs text-gray-400 block mb-1">Y Position (%)</label>
          <a-slider
            :value="layer.properties.position.y"
            @change="(value) => $emit('update', 'properties.position.y', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <!-- Image Layer Properties -->
    <div v-else-if="layer.type === 'image'" class="space-y-2">
      <h5 class="text-xs font-medium text-gray-300 border-b border-gray-600 pb-1">IMAGE SETTINGS</h5>
      
      <!-- Image Source -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Image Source</label>
        <a-input
          :value="layer.properties.src"
          @change="(e) => $emit('update', 'properties.src', e.target.value)"
          placeholder="Image path or URL"
          size="small"
        />
      </div>
      
      <!-- Scale -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Scale (%)</label>
        <a-slider
          :value="layer.properties.scale"
          @change="(value) => $emit('update', 'properties.scale', value)"
          :min="10"
          :max="200"
          size="small"
        />
      </div>
      
      <!-- Rotation -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Rotation (°)</label>
        <a-slider
          :value="layer.properties.rotation"
          @change="(value) => $emit('update', 'properties.rotation', value)"
          :min="-180"
          :max="180"
          size="small"
        />
      </div>
      
      <!-- Position -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <label class="text-xs text-gray-400 block mb-1">X Position (%)</label>
          <a-slider
            :value="layer.properties.position.x"
            @change="(value) => $emit('update', 'properties.position.x', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
        <div>
          <label class="text-xs text-gray-400 block mb-1">Y Position (%)</label>
          <a-slider
            :value="layer.properties.position.y"
            @change="(value) => $emit('update', 'properties.position.y', value)"
            :min="0"
            :max="100"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <!-- Video Layer Properties -->
    <div v-else-if="layer.type === 'video'" class="space-y-2">
      <h5 class="text-xs font-medium text-gray-300 border-b border-gray-600 pb-1">VIDEO SETTINGS</h5>
      
      <!-- Volume -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Volume (%)</label>
        <a-slider
          :value="layer.properties.volume"
          @change="(value) => $emit('update', 'properties.volume', value)"
          :min="0"
          :max="200"
          size="small"
        />
      </div>
      
      <!-- Speed -->
      <div>
        <label class="text-xs text-gray-400 block mb-1">Speed</label>
        <a-slider
          :value="layer.properties.speed"
          @change="(value) => $emit('update', 'properties.speed', value)"
          :min="0.25"
          :max="4"
          :step="0.25"
          size="small"
        />
      </div>
      
      <!-- Filters -->
      <div v-if="layer.properties.filters" class="space-y-2">
        <h6 class="text-xs text-gray-400">Filters</h6>
        
        <div>
          <label class="text-xs text-gray-400 block mb-1">Brightness</label>
          <a-slider
            :value="layer.properties.filters.brightness"
            @change="(value) => $emit('update', 'properties.filters.brightness', value)"
            :min="-1"
            :max="1"
            :step="0.1"
            size="small"
          />
        </div>
        
        <div>
          <label class="text-xs text-gray-400 block mb-1">Contrast</label>
          <a-slider
            :value="layer.properties.filters.contrast"
            @change="(value) => $emit('update', 'properties.filters.contrast', value)"
            :min="0"
            :max="3"
            :step="0.1"
            size="small"
          />
        </div>
        
        <div>
          <label class="text-xs text-gray-400 block mb-1">Saturation</label>
          <a-slider
            :value="layer.properties.filters.saturation"
            @change="(value) => $emit('update', 'properties.filters.saturation', value)"
            :min="0"
            :max="3"
            :step="0.1"
            size="small"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  layer: {
    type: Object,
    required: true
  }
})

defineEmits(['update'])
</script>

<style scoped>
.layer-properties {
  font-size: 12px;
}

.layer-properties :deep(.ant-slider) {
  margin: 0;
}

.layer-properties :deep(.ant-input-number) {
  width: 100%;
}

.layer-properties :deep(.ant-select) {
  width: 100%;
}
</style>
