<template>
  <div
    class="timeline-playhead absolute top-0 bottom-0 pointer-events-none z-20"
    :style="{
      left: playheadPosition + 'px',
      opacity: timelineStore.currentTime >= 0 ? 1 : 0
    }"
  >
    <!-- Playhead line -->
    <div class="w-px bg-red-500 h-full relative">
      <!-- Playhead handle -->
      <div
        class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-500 rounded-full cursor-pointer pointer-events-auto"
        @mousedown="startDrag"
        @click.stop
      >
        <!-- Playhead triangle -->
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-red-500"></div>
      </div>

      <!-- Time display -->
      <div
        v-if="showTimeDisplay"
        class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-auto"
      >
        {{ formatTime(timelineStore.currentTime) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'

const timelineStore = useTimelineStore()

const isDragging = ref(false)
const showTimeDisplay = ref(false)
const dragStartX = ref(0)
const dragStartTime = ref(0)

// Computed
const playheadPosition = computed(() => {
  return timelineStore.timeToPixel(timelineStore.currentTime)
})

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00.000'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

const startDrag = (event) => {
  event.preventDefault()
  event.stopPropagation()

  isDragging.value = true
  showTimeDisplay.value = true
  dragStartX.value = event.clientX
  dragStartTime.value = timelineStore.currentTime

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  document.body.style.cursor = 'grabbing'
  document.body.style.userSelect = 'none'
}

const handleDrag = (event) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value
  const deltaTime = timelineStore.pixelToTime(deltaX)
  const newTime = Math.max(0, Math.min(timelineStore.duration, dragStartTime.value + deltaTime))

  // Snap to grid if enabled
  const snappedTime = timelineStore.snapToGrid ?
    timelineStore.getSnapTime(newTime) : newTime

  timelineStore.setCurrentTime(snappedTime)
}

const stopDrag = () => {
  isDragging.value = false
  showTimeDisplay.value = false

  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// Click on timeline to seek
const handleTimelineClick = (event) => {
  if (isDragging.value) return

  const timelineElement = event.currentTarget
  const rect = timelineElement.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const clickTime = timelineStore.pixelToTime(clickX)

  // Snap to grid if enabled
  const snappedTime = timelineStore.snapToGrid ?
    timelineStore.getSnapTime(clickTime) : clickTime

  const clampedTime = Math.max(0, Math.min(timelineStore.duration, snappedTime))
  timelineStore.setCurrentTime(clampedTime)
}

// Lifecycle
onMounted(() => {
  // Add click handler to timeline container
  const timelineContainer = document.querySelector('.timeline-tracks')
  if (timelineContainer) {
    timelineContainer.addEventListener('click', handleTimelineClick)

    onUnmounted(() => {
      timelineContainer.removeEventListener('click', handleTimelineClick)
      document.removeEventListener('mousemove', handleDrag)
      document.removeEventListener('mouseup', stopDrag)
    })
  }
})
</script>

<style scoped>
.timeline-playhead {
  transition: left 0.1s ease-out;
}

.timeline-playhead:hover .absolute {
  opacity: 1;
}
</style>
