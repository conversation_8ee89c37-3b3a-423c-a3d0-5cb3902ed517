# Timeline Testing Guide

## Các vấn đề đã được sửa

### 1. ✅ Playhead Position
**Vấn đề:** Playhead không hiển thị đúng vị trí khi zoom và scroll
**Giải pháp:** 
- S<PERSON><PERSON> lại cách tính toán position trong `TimelinePlayhead.vue`
- Loại bỏ `scrollLeft` khỏi calculation vì container đã handle scroll

### 2. ✅ Timeline Track Layout
**Vấn đề:** Timeline track có width và transform không cần thiết
**Giải pháp:**
- Sửa TimelineTrack từ `relative` với `transform` thành `absolute inset-0`
- Container timeline-content handle width và scroll

### 3. ✅ Duration Calculation
**Vấn đề:** Timeline hiển thị 38 phút khi không có data, quá dài khi có data
**Giải pháp:**
- Tính duration dựa trên max endTime của subtitles + 2s buffer
- Default 30s thay vì 60s khi không có data
- Minimum 30s để tránh timeline quá ngắn

### 4. ✅ Ruler Sync
**Vấn đề:** Ruler không sync với scroll
**Giải pháp:**
- Tạo container riêng cho ruler marks với transform
- Ruler background fixed, chỉ marks di chuyển

## Cấu trúc mới

```
TimelineEditor
├── TimelineToolbar (fixed)
├── TimelineRuler (fixed background, scrolling marks)
└── TimelineTracksContainer (scrollable)
    └── TimelineContent (proper width)
        ├── TimelineTrack (absolute positioned)
        │   ├── Grid lines
        │   ├── TimelineItems
        │   └── Selection rectangle
        └── TimelinePlayhead (absolute positioned)
```

## Test Cases

### 1. Basic Display
- [ ] Timeline hiển thị với height phù hợp
- [ ] Không có data: timeline 30s
- [ ] Có data: timeline = max endTime + 2s
- [ ] Grid lines hiển thị đúng

### 2. Playhead
- [ ] Playhead ở vị trí 0 khi start
- [ ] Click timeline → playhead jump đúng vị trí
- [ ] Drag playhead → time update đúng
- [ ] Sync với video player

### 3. Zoom & Scroll
- [ ] Zoom in/out hoạt động mượt
- [ ] Scroll horizontal hoạt động
- [ ] Ruler sync với scroll
- [ ] Playhead vẫn đúng vị trí sau zoom/scroll

### 4. Subtitle Items
- [ ] Items hiển thị đúng position
- [ ] Items có đúng width theo duration
- [ ] Drag items hoạt động
- [ ] Resize handles hoạt động
- [ ] Selection hoạt động

### 5. Grid & Snap
- [ ] Grid lines hiển thị đúng
- [ ] Snap to grid hoạt động
- [ ] Grid size thay đổi được

## Debugging Commands

### 1. Check Timeline State
```javascript
// In browser console
const timelineStore = window.$nuxt.$pinia._s.get('timeline')
console.log('Timeline State:', {
  zoom: timelineStore.zoom,
  duration: timelineStore.duration,
  scrollLeft: timelineStore.scrollLeft,
  timelineWidth: timelineStore.timelineWidth,
  totalTimelineWidth: timelineStore.totalTimelineWidth
})
```

### 2. Check Subtitle Data
```javascript
const ttsStore = window.$nuxt.$pinia._s.get('tts')
console.log('Subtitle Items:', ttsStore.currentSrtList?.items)
```

### 3. Check Playhead Position
```javascript
const playhead = document.querySelector('.timeline-playhead')
console.log('Playhead Style:', playhead?.style.left)
```

## Performance Optimization

### 1. Virtualization (nếu cần)
Với >1000 subtitle items:
```javascript
const visibleItems = computed(() => {
  const range = timelineStore.visibleTimeRange
  return subtitleItems.value.filter(item => 
    item.endTime >= range.start && item.startTime <= range.end
  )
})
```

### 2. Debounced Updates
```javascript
import { debounce } from 'lodash-es'

const debouncedUpdate = debounce((item, startTime, endTime) => {
  updateItemTiming(item, startTime, endTime)
}, 16) // 60fps
```

## Common Issues & Solutions

### Issue: Playhead không sync với video
**Solution:** Check `state.videoPlayer` reference
```javascript
// In TimelineEditor.vue
watch(() => state.currentTime, (newTime) => {
  if (newTime !== undefined) {
    timelineStore.setCurrentTime(newTime)
  }
})
```

### Issue: Timeline quá rộng
**Solution:** Check duration calculation
```javascript
// In updateDuration()
const maxEndTime = Math.max(...subtitleItems.value.map(item => item.endTime || 0))
timelineStore.setDuration(Math.max(maxEndTime + 2, 30))
```

### Issue: Drag không hoạt động
**Solution:** Check event listeners và z-index
```javascript
// TimelineItem should have higher z-index when selected
zIndex: isSelected.value ? 10 : 1
```

### Issue: Grid không hiển thị
**Solution:** Check grid calculation
```javascript
const gridLines = computed(() => {
  const lines = []
  for (let time = 0; time <= timelineStore.duration; time += timelineStore.gridSize) {
    lines.push(timelineStore.timeToPixel(time))
  }
  return lines
})
```

## Browser Compatibility

### Tested Browsers
- ✅ Chrome 120+
- ✅ Firefox 120+
- ✅ Safari 17+
- ✅ Edge 120+

### Known Issues
- Safari: CSS `inset-0` có thể cần fallback
- Firefox: Scroll performance có thể chậm với nhiều items

## Next Steps

1. **Test với data thực tế**
   - Import SRT file
   - Check timeline hiển thị đúng
   - Test drag/resize operations

2. **Performance testing**
   - Test với 100+ subtitle items
   - Check scroll performance
   - Memory usage

3. **Integration testing**
   - Sync với video player
   - Audio generation status
   - Translation status

4. **User experience**
   - Keyboard shortcuts
   - Context menu actions
   - Undo/redo functionality
