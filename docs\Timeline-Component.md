# Timeline Component Documentation

## Tổng quan

Timeline Component l<PERSON> một hệ thống chỉnh sửa subtitle chuyê<PERSON> nghiệp đư<PERSON><PERSON> tích hợp vào PanelPage.vue. Component này cung cấp đầy đủ các tính năng để chỉnh sửa subtitle như các ứng dụng chỉnh sửa video chuyên nghiệp.

## C<PERSON>u trúc Component

### 1. TimelineEditor.vue (Component chính)
- Quản lý toàn bộ timeline
- Xử lý keyboard shortcuts
- T<PERSON><PERSON> hợp với video player
- Quản lý context menu

### 2. TimelineToolbar.vue
- Thanh công cụ với playback controls
- Zoom controls
- Grid settings
- Time display

### 3. TimelineRuler.vue
- Thước đo thời gian
- Major/minor tick marks
- Current time indicator
- Responsive theo zoom level

### 4. TimelineTrack.vue
- Container cho subtitle items
- Selection rectangle
- Grid lines
- Drag & drop handling

### 5. TimelineItem.vue
- Hiển thị từng subtitle
- Resize handles
- Visual status indicators
- Drag & drop support

### 6. TimelinePlayhead.vue
- <PERSON> chỉ thời gian hiện tại
- Draggable để seek
- Sync với video player

### 7. TimelineContextMenu.vue
- Right-click menu
- Actions cho subtitle editing
- Batch operations

### 8. timeline-store.js (Pinia Store)
- Quản lý state của timeline
- Zoom, scroll, selection
- History/undo-redo
- Grid và snap settings

## Tính năng chính

### 1. Hiển thị và Navigation
- ✅ Hiển thị subtitle items dưới dạng blocks
- ✅ Zoom in/out với mouse wheel + Ctrl
- ✅ Scroll horizontal
- ✅ Ruler với time markers
- ✅ Grid lines và snap to grid
- ✅ Playhead sync với video

### 2. Selection
- ✅ Single click để select
- ✅ Ctrl+click để multi-select
- ✅ Shift+click để range select
- ✅ Drag selection rectangle
- ✅ Select all (Ctrl+A)

### 3. Drag & Drop
- ✅ Drag để di chuyển subtitle
- ✅ Resize handles để thay đổi timing
- ✅ Snap to grid
- ✅ Prevent overlap
- ✅ Multi-item drag

### 4. Editing Operations
- 🔄 Split subtitle
- 🔄 Merge subtitles
- 🔄 Insert new subtitle
- 🔄 Delete subtitles
- 🔄 Copy/paste
- 🔄 Duplicate

### 5. Playback Control
- ✅ Play/pause
- ✅ Seek to start/end
- ✅ Seek forward/backward
- ✅ Click timeline để seek

### 6. History
- ✅ Undo/Redo (Ctrl+Z, Ctrl+Shift+Z)
- ✅ Auto-save state before operations

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Space` | Play/Pause |
| `Home` | Go to start |
| `End` | Go to end |
| `Left Arrow` | Seek backward 5s |
| `Right Arrow` | Seek forward 5s |
| `Ctrl+A` | Select all |
| `Ctrl+Z` | Undo |
| `Ctrl+Shift+Z` | Redo |
| `Delete` | Delete selected |
| `Escape` | Clear selection |
| `Ctrl+Wheel` | Zoom |

## Cách sử dụng

### 1. Tích hợp vào PanelPage
```vue
<template>
  <div class="flex-1 bg-gray-750 h-full min-h-0">
    <TimelineEditor />
  </div>
</template>

<script setup>
import TimelineEditor from './TimelineEditor.vue'
</script>
```

### 2. Sync với Video Player
Timeline tự động sync với video player thông qua `state.videoPlayer` và `state.currentTime`.

### 3. Dữ liệu Subtitle
Timeline sử dụng `ttsStore.currentSrtList.items` làm nguồn dữ liệu.

## Cấu trúc dữ liệu Subtitle

```javascript
const subtitle = {
  id: 1,
  index: 1,
  start: '00:00:01,000',    // SRT format
  end: '00:00:03,000',      // SRT format
  startTime: 1.0,           // seconds
  endTime: 3.0,             // seconds
  text: 'Original text',
  translatedText: 'Translated text',
  status: 'translated',     // pending, translating, translated, error
  isGenerated1: true,       // Voice 1 audio generated
  isGenerated2: false,      // Voice 2 audio generated
  isGenerated3: false,      // Voice 3 audio generated
}
```

## Timeline Store API

### State
- `zoom`: Zoom level (1 = 100px per second)
- `scrollLeft`: Horizontal scroll position
- `currentTime`: Playhead position
- `selectedItems`: Array of selected subtitle IDs
- `snapToGrid`: Enable/disable snap to grid
- `gridSize`: Grid size in seconds

### Actions
- `setZoom(zoom)`: Set zoom level
- `zoomIn()` / `zoomOut()`: Zoom controls
- `fitToView()`: Fit timeline to container
- `setCurrentTime(time)`: Set playhead position
- `selectItem(id)` / `selectMultiple(ids)`: Selection
- `saveState()` / `undo()` / `redo()`: History

## Customization

### 1. Colors và Styling
Sửa đổi CSS variables trong các component để thay đổi màu sắc:

```css
.timeline-item {
  --color-pending: #6b7280;
  --color-translated: #10b981;
  --color-translating: #f59e0b;
  --color-error: #ef4444;
  --color-selected: #3b82f6;
}
```

### 2. Grid Settings
Thay đổi grid size options trong TimelineToolbar:

```vue
<select v-model="timelineStore.gridSize">
  <option value="0.1">100ms</option>
  <option value="0.5">500ms</option>
  <option value="1">1s</option>
</select>
```

### 3. Track Height
Sửa đổi `trackHeight` trong timeline store:

```javascript
state: () => ({
  trackHeight: 60, // pixels
})
```

## Tích hợp với các Component khác

### 1. Video Player
```javascript
// Sync timeline với video
watch(() => state.currentTime, (newTime) => {
  timelineStore.setCurrentTime(newTime)
})
```

### 2. Subtitle Table
```javascript
// Update subtitle data
const updateSubtitle = (id, changes) => {
  const item = ttsStore.currentSrtList.items.find(i => i.id === id)
  Object.assign(item, changes)
}
```

## Performance

### 1. Virtualization
Với nhiều subtitle items (>1000), có thể implement virtualization:

```javascript
// Chỉ render items trong viewport
const visibleItems = computed(() => {
  const range = timelineStore.visibleTimeRange
  return subtitleItems.value.filter(item => 
    item.endTime >= range.start && item.startTime <= range.end
  )
})
```

### 2. Debouncing
Debounce các operations như drag để tránh lag:

```javascript
const debouncedUpdate = debounce(updateItemTiming, 16) // 60fps
```

## Troubleshooting

### 1. Timeline không hiển thị
- Kiểm tra `ttsStore.currentSrtList` có data
- Kiểm tra container có height đúng
- Kiểm tra timeline duration được set

### 2. Drag không hoạt động
- Kiểm tra event listeners được add đúng
- Kiểm tra z-index của items
- Kiểm tra pointer-events CSS

### 3. Sync với video bị lag
- Kiểm tra `state.videoPlayer` reference
- Kiểm tra video element có tồn tại
- Reduce update frequency nếu cần

## Roadmap

### Phase 1 (Completed)
- ✅ Basic timeline display
- ✅ Zoom và scroll
- ✅ Selection
- ✅ Drag & drop
- ✅ Playhead sync

### Phase 2 (In Progress)
- 🔄 Context menu actions
- 🔄 Split/merge operations
- 🔄 Insert/delete operations
- 🔄 Copy/paste

### Phase 3 (Planned)
- 📋 Waveform display
- 📋 Video thumbnails
- 📋 Multiple tracks
- 📋 Keyframe animation
- 📋 Export timeline data
