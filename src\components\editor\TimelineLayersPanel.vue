<template>
  <div class="timeline-layers-panel bg-gray-800 border-t border-gray-600">
    <!-- Header -->
    <div class="layers-header flex items-center justify-between p-2 bg-gray-900 border-b border-gray-600">
      <h4 class="text-sm font-medium text-gray-200">Timeline Layers</h4>
      <div class="flex items-center gap-2">
        <a-button size="small" @click="addTextLayer">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="4,7 4,4 20,4 20,7"/>
              <line x1="9" y1="20" x2="15" y2="20"/>
              <line x1="12" y1="4" x2="12" y2="20"/>
            </svg>
          </template>
          Text
        </a-button>
        
        <a-button size="small" @click="addImageLayer">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
          </template>
          Image
        </a-button>
        
        <a-button size="small" @click="addEffect">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </template>
          Effect
        </a-button>
      </div>
    </div>
    
    <!-- Timeline Tracks -->
    <div class="timeline-tracks flex-1 overflow-auto max-h-40">
      <!-- Video Track -->
      <div class="timeline-track">
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="toggleTrack('video')">
              <template #icon>
                <svg v-if="videoTrackEnabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="23 7 16 12 23 17 23 7"/>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M16 16v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>
            <span class="text-xs text-gray-300">Video</span>
          </div>
        </div>
        
        <div class="track-content relative h-8 bg-gray-800">
          <!-- Video timeline bar -->
          <div 
            class="timeline-item absolute h-6 mt-1 bg-blue-600 rounded flex items-center px-2"
            :style="{ width: '100%', left: '0%' }"
          >
            <span class="text-xs text-white truncate">{{ videoLayer?.name || 'Video' }}</span>
          </div>
        </div>
      </div>
      
      <!-- Subtitle Track -->
      <div class="timeline-track">
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="toggleTrack('subtitle')">
              <template #icon>
                <svg v-if="subtitleTrackEnabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>
            <span class="text-xs text-gray-300">Subtitles</span>
          </div>
        </div>
        
        <div class="track-content relative h-8 bg-gray-800">
          <!-- Subtitle items -->
          <div
            v-for="subtitle in subtitleItems"
            :key="subtitle.id"
            class="timeline-item absolute h-6 mt-1 bg-green-600 rounded flex items-center px-1 cursor-pointer hover:bg-green-500"
            :style="getSubtitleStyle(subtitle)"
            @click="selectSubtitle(subtitle)"
          >
            <span class="text-xs text-white truncate">{{ subtitle.translatedText?.substring(0, 20) }}...</span>
          </div>
        </div>
      </div>
      
      <!-- Layer Tracks -->
      <div
        v-for="layer in customLayers"
        :key="layer.id"
        class="timeline-track"
      >
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="toggleLayer(layer.id)">
              <template #icon>
                <svg v-if="layer.enabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>
            
            <div class="layer-icon">
              <svg v-if="layer.type === 'text'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
              <svg v-else-if="layer.type === 'image'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
              </svg>
              <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              </svg>
            </div>
            
            <span class="text-xs text-gray-300 truncate">{{ layer.name }}</span>
          </div>
          
          <div class="track-actions flex items-center gap-1">
            <a-button size="small" type="text" @click="deleteLayer(layer.id)">
              <template #icon>
                <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"/>
                  <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                </svg>
              </template>
            </a-button>
          </div>
        </div>
        
        <div class="track-content relative h-8 bg-gray-800">
          <!-- Layer timeline bar -->
          <div 
            class="timeline-item absolute h-6 mt-1 rounded flex items-center px-2 cursor-pointer"
            :class="getLayerClasses(layer)"
            :style="getLayerStyle(layer)"
            @click="selectLayer(layer)"
          >
            <span class="text-xs text-white truncate">{{ layer.name }}</span>
          </div>
        </div>
      </div>
      
      <!-- Effects Track -->
      <div v-if="allEffects.length > 0" class="timeline-track">
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
            <span class="text-xs text-gray-300">Effects</span>
          </div>
        </div>
        
        <div class="track-content relative h-8 bg-gray-800">
          <!-- Effect items -->
          <div
            v-for="effect in allEffects"
            :key="effect.id"
            class="timeline-item absolute h-6 mt-1 rounded flex items-center px-1 cursor-pointer"
            :class="getEffectClasses(effect)"
            :style="getEffectStyle(effect)"
            @click="selectEffect(effect)"
          >
            <span class="text-xs text-white truncate">{{ effect.type.toUpperCase() }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { useTTSStore } from '@/stores/ttsStore'
import { parseTimeToSeconds } from '@/lib/utils'

const layersStore = useVideoLayersStore()
const ttsStore = useTTSStore()

// Computed
const videoLayer = computed(() => layersStore.videoLayer)
const subtitleLayer = computed(() => layersStore.subtitleLayer)
const customLayers = computed(() => 
  layersStore.layers.filter(layer => !['video', 'subtitle'].includes(layer.type))
)

const subtitleItems = computed(() => ttsStore.currentSrtList?.items || [])

const allEffects = computed(() => {
  const effects = layersStore.effects
  return [
    ...effects.delogoAreas.map(e => ({ ...e, type: 'delogo' })),
    ...effects.blurAreas.map(e => ({ ...e, type: 'blur' })),
    ...effects.textOverlays.map(e => ({ ...e, type: 'text' })),
    ...effects.imageOverlays.map(e => ({ ...e, type: 'image' }))
  ]
})

const videoTrackEnabled = computed(() => videoLayer.value?.enabled)
const subtitleTrackEnabled = computed(() => subtitleLayer.value?.enabled)

// Methods
const toggleTrack = (type) => {
  if (type === 'video') {
    layersStore.toggleLayer('video-bg')
  } else if (type === 'subtitle') {
    layersStore.toggleLayer('subtitles')
  }
}

const toggleLayer = (id) => {
  layersStore.toggleLayer(id)
}

const selectLayer = (layer) => {
  layersStore.selectLayer(layer.id)
}

const selectEffect = (effect) => {
  layersStore.selectEffect(effect.id)
}

const selectSubtitle = (subtitle) => {
  // TODO: Seek to subtitle time
  console.log('Select subtitle:', subtitle)
}

const deleteLayer = (id) => {
  layersStore.removeLayer(id)
}

const addTextLayer = () => {
  layersStore.addLayer({
    type: 'text',
    name: 'Text Layer',
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      position: { x: 50, y: 50 }
    }
  })
}

const addImageLayer = () => {
  layersStore.addLayer({
    type: 'image',
    name: 'Image Layer',
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100
    }
  })
}

const addEffect = () => {
  layersStore.addEffect('blur', {
    x: 0.1,
    y: 0.1,
    width: 0.2,
    height: 0.2,
    intensity: 10
  })
}

// Styling helpers
const getLayerClasses = (layer) => ({
  'bg-purple-600 hover:bg-purple-500': layer.type === 'text',
  'bg-orange-600 hover:bg-orange-500': layer.type === 'image',
  'bg-gray-600 hover:bg-gray-500': !['text', 'image'].includes(layer.type),
  'ring-2 ring-white': layersStore.selectedLayerId === layer.id
})

const getLayerStyle = (layer) => {
  const duration = layersStore.videoInfo.duration || 100
  const startPercent = (layer.timeRange.start / duration) * 100
  const widthPercent = ((layer.timeRange.end - layer.timeRange.start) / duration) * 100
  
  return {
    left: startPercent + '%',
    width: widthPercent + '%'
  }
}

const getSubtitleStyle = (subtitle) => {
  const duration = layersStore.videoInfo.duration || 100
  const startTime = parseTimeToSeconds(subtitle.start)
  const endTime = parseTimeToSeconds(subtitle.end)
  
  const startPercent = (startTime / duration) * 100
  const widthPercent = ((endTime - startTime) / duration) * 100
  
  return {
    left: startPercent + '%',
    width: Math.max(widthPercent, 2) + '%' // Minimum 2% width
  }
}

const getEffectClasses = (effect) => ({
  'bg-blue-600 hover:bg-blue-500': effect.type === 'blur',
  'bg-red-600 hover:bg-red-500': effect.type === 'delogo',
  'bg-green-600 hover:bg-green-500': effect.type === 'color',
  'bg-yellow-600 hover:bg-yellow-500': effect.type === 'crop',
  'ring-2 ring-white': layersStore.selectedEffectId === effect.id
})

const getEffectStyle = (effect) => {
  const duration = layersStore.videoInfo.duration || 100
  const startPercent = (effect.timeStart / duration) * 100
  const widthPercent = ((effect.timeEnd - effect.timeStart) / duration) * 100
  
  return {
    left: startPercent + '%',
    width: Math.max(widthPercent, 2) + '%' // Minimum 2% width
  }
}
</script>

<style scoped>
.timeline-track {
  border-bottom: 1px solid #374151;
}

.timeline-item {
  transition: all 0.2s ease;
  min-width: 20px;
}

.timeline-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.track-content {
  position: relative;
  overflow: hidden;
}

.track-header {
  min-height: 32px;
}
</style>
