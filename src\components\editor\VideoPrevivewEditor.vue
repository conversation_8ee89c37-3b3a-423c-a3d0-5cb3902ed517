<template>
  <div class="video-preview-editor h-full w-full flex flex-col bg-gray-900 text-white">
    <!-- Header Controls -->
    <div class="video-header flex items-center justify-between p-2 bg-gray-800 border-b border-gray-600">
      <div class="flex items-center gap-2">
        <h3 class="text-sm font-medium">Video Preview</h3>
        <span v-if="videoInfo.duration" class="text-xs text-gray-400">
          {{ formatTime(videoInfo.duration) }}
        </span>
        <span v-if="countAll > 0" class="text-xs text-gray-400">
          • {{ countAll }} items
        </span>
      </div>

      <!-- View Controls -->
      <div class="flex items-center gap-2">
        <!-- Quality & Zoom -->
        <a-select v-model:value="previewQuality" size="small" style="width: 80px">
          <a-select-option value="low">Low</a-select-option>
          <a-select-option value="medium">Med</a-select-option>
          <a-select-option value="high">High</a-select-option>
        </a-select>

        <a-select v-model:value="previewZoom" size="small" style="width: 70px">
          <a-select-option :value="50">50%</a-select-option>
          <a-select-option :value="75">75%</a-select-option>
          <a-select-option :value="100">100%</a-select-option>
          <a-select-option :value="125">125%</a-select-option>
        </a-select>

        <a-divider type="vertical" />

        <!-- Quick Toggles -->
        <a-button-group size="small">
          <a-button
            :type="showSubtitles ? 'primary' : 'default'"
            @click="showSubtitles = !showSubtitles"
          >
            Subtitle
          </a-button>
          <a-button
            :type="showGrid ? 'primary' : 'default'"
            @click="showGrid = !showGrid"
          >
            Grid
          </a-button>
          <a-button
            :type="showSafeArea ? 'primary' : 'default'"
            @click="showSafeArea = !showSafeArea"
          >
            Safe
          </a-button>
        </a-button-group>

        <!-- Hide/Show -->
        <a-button size="small" @click="isMinimized = !isMinimized">
          <template #icon>
            <ChevronDown v-if="!isMinimized" class="w-4 h-4" />
            <ChevronUp v-if="isMinimized" class="w-4 h-4" />
          </template>
        </a-button>
      </div>
    </div>

    <!-- Main Content -->
    <div v-if="!isMinimized && ttsStore.currentSrtList" class="flex-1 flex flex-col min-h-0">
      <!-- Video Preview Area -->
      <div class="flex-1 flex items-center justify-center p-4 relative bg-gray-900">
        <div
          class="video-viewport relative bg-black rounded overflow-hidden shadow-lg"
          :style="viewportStyle"
          @click="handleVideoClick"
        >
          <!-- Video Element -->
          <VideoPlayerEditor
            ref="videoPlayer"
            :src="videoSrc"
            :click-play="false"
            @timeupdate="handleVideoTimeUpdate"
            @loadedmetadata="onVideoLoaded"
            size="full"
            class="w-full h-full"
          />

          <!-- Overlay Layers -->
          <div class="absolute inset-0 pointer-events-none">
            <!-- Subtitle Layer -->
            <SubtitleOverlay
              v-if="showSubtitles"
              :current-time="currentTime"
              :video-dimensions="videoDimensions"
              @subtitle-click="handleSubtitleClick"
            />

            <!-- Text Overlays -->
            <TextOverlay
              v-for="textLayer in enabledTextLayers"
              :key="textLayer.id"
              :layer="textLayer"
              :current-time="currentTime"
              :video-dimensions="videoDimensions"
              @text-click="handleTextClick"
            />

            <!-- Image Overlays -->
            <ImageOverlay
              v-for="imageLayer in enabledImageLayers"
              :key="imageLayer.id"
              :layer="imageLayer"
              :current-time="currentTime"
              :video-dimensions="videoDimensions"
              @image-click="handleImageClick"
            />

            <!-- Effect Overlays -->
            <EffectOverlay
              v-for="effect in activeEffects"
              :key="effect.id"
              :effect="effect"
              :current-time="currentTime"
              :video-dimensions="videoDimensions"
            />
          </div>

          <!-- Grid Overlay -->
          <GridOverlay
            v-if="showGrid"
            :video-dimensions="videoDimensions"
          />

          <!-- Safe Area Overlay -->
          <SafeAreaOverlay
            v-if="showSafeArea"
            :video-dimensions="videoDimensions"
          />
        </div>
      </div>

      <!-- Progress Footer -->
      <div class="progress-footer p-3 bg-gray-800 border-t border-gray-600">
        <div class="flex items-center gap-4 text-xs">
          <!-- Progress Bars -->
          <div class="flex items-center gap-2">
            <span>Dịch:</span>
            <a-progress
              :percent="translatedText"
              :format="(percent) => `${percent}%`"
              :size="[80, 12]"
            />
          </div>
          <div class="flex items-center gap-2">
            <span>TTS:</span>
            <a-progress
              :percent="audioProgress"
              :format="(percent) => `${percent}%`"
              :size="[80, 12]"
              stroke-color="#B7EB8F"
            />
          </div>

          <div class="flex-1"></div>

          <!-- Quick Actions -->
          <a-button size="small" @click="addTextOverlay">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
            </template>
            Add Text
          </a-button>

          <a-button size="small" @click="addImageOverlay">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
              </svg>
            </template>
            Add Image
          </a-button>

          <a-button type="primary" size="small" @click="renderVideo">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polygon points="10,8 16,12 10,16 10,8"/>
              </svg>
            </template>
            Render
          </a-button>
        </div>
      </div>
    </div>

    <!-- Minimized Progress -->
    <div v-if="isMinimized && ttsStore.currentSrtList" class="p-2 space-y-1">
      <div class="flex gap-2 text-xs">
        <span class="w-12">Dịch:</span>
        <a-progress :percent="translatedText" :size="[100, 8]" />
      </div>
      <div class="flex gap-2 text-xs">
        <span class="w-12">TTS:</span>
        <a-progress :percent="audioProgress" :size="[100, 8]" stroke-color="#B7EB8F" />
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!ttsStore.currentSrtList" class="flex-1 flex flex-col items-center justify-center text-gray-400 p-4">
      <div class="text-center py-8">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="mx-auto mb-4 opacity-50">
          <polygon points="23 7 16 12 23 17 23 7"/>
          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
        </svg>
        <p class="mb-2 text-lg">No Video Selected</p>
        <p class="text-sm opacity-75">Import an SRT file to start editing</p>
      </div>
    </div>

    <!-- Video Renderer Modal -->
    <VideoRenderer
      v-model:visible="showRenderModal"
      @render-complete="handleRenderComplete"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed, onBeforeUnmount, onMounted } from 'vue'
import { useI18n } from '@/i18n/i18n'
import { useTTSStore } from '@/stores/ttsStore'
import { useSubtitleStore } from '@/stores/subtitle-store'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'
import { state } from '@/lib/state'
import { parseTimeToSeconds } from '@/lib/utils'

// Components
import VideoPlayerEditor from './VideoPlayerEditor.vue'
import SubtitleOverlay from './overlays/SubtitleOverlay.vue'
import TextOverlay from './overlays/TextOverlay.vue'
import ImageOverlay from './overlays/ImageOverlay.vue'
import EffectOverlay from './overlays/EffectOverlay.vue'
import GridOverlay from './overlays/GridOverlay.vue'
import SafeAreaOverlay from './overlays/SafeAreaOverlay.vue'
import VideoRenderer from './VideoRenderer.vue'

// Stores
const { t } = useI18n()
const ttsStore = useTTSStore()
const subtitleStore = useSubtitleStore()
const layersStore = useVideoLayersStore()

// Refs
const videoPlayer = ref(null)

// Local state
const previewQuality = ref('medium')
const previewZoom = ref(100)
const isMinimized = ref(false)
const showSubtitles = ref(true)
const showGrid = ref(false)
const showSafeArea = ref(false)

// Progress tracking
const audioProgress = ref(0)
const translatedText = ref(0)
const countAll = ref(0)
const activeSubtitleId = ref(null)
const isManualSeek = ref(false)
const currentTime = ref(0)

// Set video player reference for global state
state.videoPlayer = videoPlayer

// Computed properties
const videoInfo = computed(() => layersStore.videoInfo)
const videoSrc = computed(() =>
  ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4') || ''
)
const videoDimensions = computed(() => ({
  width: videoInfo.value.width || 1920,
  height: videoInfo.value.height || 1080
}))

const enabledTextLayers = computed(() =>
  layersStore.enabledLayers.filter(layer => layer.type === 'text')
)
const enabledImageLayers = computed(() =>
  layersStore.enabledLayers.filter(layer => layer.type === 'image')
)
const activeEffects = computed(() => {
  const effects = layersStore.activeEffects
  return [
    ...effects.delogo.map(e => ({ ...e, type: 'delogo' })),
    ...effects.blur.map(e => ({ ...e, type: 'blur' })),
    ...effects.text.map(e => ({ ...e, type: 'text' })),
    ...effects.image.map(e => ({ ...e, type: 'image' }))
  ]
})

const viewportStyle = computed(() => {
  const zoom = previewZoom.value / 100
  const maxWidth = 600 * zoom
  const maxHeight = 400 * zoom

  return {
    maxWidth: maxWidth + 'px',
    maxHeight: maxHeight + 'px',
    width: '100%',
    aspectRatio: '16/9'
  }
})

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const onVideoLoaded = () => {
  if (videoPlayer.value?.$refs?.video) {
    const video = videoPlayer.value.$refs.video
    layersStore.setVideoInfo({
      width: video.videoWidth || 1920,
      height: video.videoHeight || 1080,
      duration: video.duration || 0,
      src: video.src
    })
  }
}

const handleVideoClick = (event) => {
  const rect = event.currentTarget.getBoundingClientRect()
  const x = ((event.clientX - rect.left) / rect.width) * 100
  const y = ((event.clientY - rect.top) / rect.height) * 100

  console.log('Video clicked at:', { x, y })
}

const handleSubtitleClick = (subtitle) => {
  console.log('Subtitle clicked:', subtitle)
}

const handleTextClick = (layer) => {
  console.log('Text layer clicked:', layer)
}

const handleImageClick = (layer) => {
  console.log('Image layer clicked:', layer)
}

const addTextOverlay = () => {
  layersStore.addLayer({
    type: 'text',
    name: 'Text Overlay',
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      position: { x: 50, y: 50 }
    }
  })
}

const addImageOverlay = () => {
  layersStore.addLayer({
    type: 'image',
    name: 'Image Overlay',
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100
    }
  })
}

const showRenderModal = ref(false)

const renderVideo = () => {
  showRenderModal.value = true
}

const handleRenderComplete = (outputPath) => {
  showRenderModal.value = false
  console.log('Video rendered to:', outputPath)
}

// Watch for subtitle items changes
watch(() => ttsStore.currentSrtList?.items, (val) => {
  if (val) {
    const translated = val.filter(item => item.status === 'translated').length
    const total = val.length
    translatedText.value = Math.floor((translated / total) * 100)
    const generated = val.filter(item => item.isVoice && item.isVoice > 0 && item[`isGenerated${item.isVoice}`]).length
    audioProgress.value = Math.floor((generated / total) * 100)
    countAll.value = total

    // Update video info if available
    if (ttsStore.currentSrtList?.path) {
      const videoPath = ttsStore.currentSrtList.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')
      layersStore.setVideoInfo({ src: videoPath })
    }
  }
})


// Watch cho external changes (từ UI clicks, buttons, etc.)
watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
  // Chỉ xử lý khi không phải từ video timeupdate
  if (!isManualSeek.value && newId !== activeSubtitleId.value) {
    activeSubtitleId.value = newId;

    // Seek video đến subtitle được chọn từ bên ngoài
    if (state.videoPlayer?.$refs.video && newId) {
      const subtitle = ttsStore.currentSrtList?.items.find(sub => sub.id === newId);
      if (subtitle) {
        const startTime = timeToSeconds(subtitle.start);
        state.videoPlayer.$refs.video.currentTime = startTime;
      }
    }
  }

  // Reset flag
  isManualSeek.value = false;
});


const updateActiveSubtitle = (currentTime) => {
  if (ttsStore.currentSrtList?.items.length === 0) return;

  // Find the subtitle currently being displayed
  const activeSubtitle = ttsStore.currentSrtList?.items.find(sub => {
    const startTime = timeToSeconds(sub.start);
    const endTime = timeToSeconds(sub.end);
    // Extend a bit to avoid boundary cases
    return currentTime >= startTime - 0.1 && currentTime <= endTime + 0.1;
  });

  // Update active subtitle ID
  const newActiveId = activeSubtitle?.id || null;
  if (newActiveId !== activeSubtitleId.value) {
    activeSubtitleId.value = newActiveId;
    // Notify parent component
    // emit('subtitleChange', newActiveId);
  }
};


let timeTrackingInterval = null;

onMounted(() => {

  // timeTrackingInterval = setInterval(() => {
  //   if (state.videoPlayer && state.videoPlayer.$refs.video && ttsStore.currentSrtList?.items.length > 0 && !state.videoPlayer.$refs.video.paused) {
  //     updateActiveSubtitle(state.videoPlayer.$refs.video.currentTime);
  //   }
  // }, 100);
});

onBeforeUnmount(() => {
  // Clear interval
  if (timeTrackingInterval) {
    clearInterval(timeTrackingInterval);
  }

});




function handleVideoTimeUpdate(time) {
  const currentSubtitle = ttsStore.currentSrtList?.items.find(item => item.startTime <= time && item.endTime >= time);

  if (currentSubtitle && currentSubtitle.id !== state.currentPlayingSubtitleId) {
    // console.log('handleVideoTimeUpdate', time, currentSubtitle);

    // Đánh dấu rằng đây là thay đổi từ video, không phải manual
    isManualSeek.value = true;

    // Cập nhật state
    state.currentPlayingSubtitleId = currentSubtitle.id;
    activeSubtitleId.value = currentSubtitle.id;
  }
}




// Convert time from "00:00:00,000" format to seconds
const timeToSeconds = (timeString) => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
};
</script>