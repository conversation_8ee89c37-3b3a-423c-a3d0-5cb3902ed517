<template>
  <div
    v-if="ttsStore.currentSrtList"
    class="h-full w-full dark:bg-gray-800 shadow-lg rounded-lg p-2 z-50"
  >
    <div class="flex justify-between items-center mb-1">
      <h3 class="text-md font-medium">Process {{ countAll }} items</h3>
      <div class="flex items-center gap-2">
        <!-- Icon drag -->
        <!-- <div class="cursor-move p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="9" cy="12" r="1"/>
            <circle cx="9" cy="5" r="1"/>
            <circle cx="9" cy="19" r="1"/>
            <circle cx="15" cy="12" r="1"/>
            <circle cx="15" cy="5" r="1"/>
            <circle cx="15" cy="19" r="1"/>
          </svg>
        </div> -->
        <!-- Nút ẩn xuống -->
        <!-- <button @click="hide" class="text-sm text-gray-500 hover:text-gray-700">
          <ChevronDown size="16" v-if="!isHide" />
          <ChevronUp size="16" v-else />
        </button> -->
      </div>
    </div>
    
    <VideoPlayerEditor
        v-if="!isHide && ttsStore.currentSrtList"
        ref="videoPlayer"
        :src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
        @timeupdate="handleVideoTimeUpdate"
        size="50"
        />
    <div class="flex gap-1">
        Dịch <a-progress :percent="translatedText" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]"  />
    </div>
    <div class="flex gap-1">
        TTS <a-progress :percent="audioProgress" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]" strokeColor="#B7EB8F" />
    </div>

  </div>
  <div v-else>
    <!-- Empty state -->
    <div class="flex flex-col items-center justify-center h-full  text-white p-4">
      <div class="text-center py-8 text-gray-500">
        <p class="mb-4">No Video file selected.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, reactive, computed, onBeforeUnmount,onMounted } from 'vue'
import { useI18n } from '@/i18n/i18n';
import { useTTSStore } from '@/stores/ttsStore';
import { Progress } from 'ant-design-vue';
import { ChevronDown, ChevronUp } from 'lucide-vue-next';
import {state } from '@/lib/state';
import { useSubtitleStore } from '@/stores/subtitle-store';

const { t } = useI18n()
const ttsStore = useTTSStore()
const subtitleStore = useSubtitleStore();

const audioProgress = ref(0)
const translatedText = ref(0)
const isHide = ref(false)
const countAll = ref(0)
const videoPlayer = ref(null)
const activeSubtitleId = ref(null);
const isManualSeek = ref(false);

state.videoPlayer = videoPlayer

watch(() => ttsStore.currentSrtList?.items, (val) => {
  if (val) {
    const translated = val.filter(item => item.status === 'translated').length;
    const total = val.length;
    translatedText.value = Math.floor((translated / total) * 100);
    const generated = val.filter(item => item.isVoice && item.isVoice > 0 && item[`isGenerated${item.isVoice}`]).length;
    audioProgress.value = Math.floor((generated / total) * 100);
    countAll.value = total;
  }
})


// Watch cho external changes (từ UI clicks, buttons, etc.)
watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
  // Chỉ xử lý khi không phải từ video timeupdate
  if (!isManualSeek.value && newId !== activeSubtitleId.value) {
    activeSubtitleId.value = newId;

    // Seek video đến subtitle được chọn từ bên ngoài
    if (state.videoPlayer?.$refs.video && newId) {
      const subtitle = ttsStore.currentSrtList?.items.find(sub => sub.id === newId);
      if (subtitle) {
        const startTime = timeToSeconds(subtitle.start);
        state.videoPlayer.$refs.video.currentTime = startTime;
      }
    }
  }
  
  // Reset flag
  isManualSeek.value = false;
});


const updateActiveSubtitle = (currentTime) => {
  if (ttsStore.currentSrtList?.items.length === 0) return;

  // Find the subtitle currently being displayed
  const activeSubtitle = ttsStore.currentSrtList?.items.find(sub => {
    const startTime = timeToSeconds(sub.start);
    const endTime = timeToSeconds(sub.end);
    // Extend a bit to avoid boundary cases
    return currentTime >= startTime - 0.1 && currentTime <= endTime + 0.1;
  });

  // Update active subtitle ID
  const newActiveId = activeSubtitle?.id || null;
  if (newActiveId !== activeSubtitleId.value) {
    activeSubtitleId.value = newActiveId;
    // Notify parent component
    // emit('subtitleChange', newActiveId);
  }
};


let timeTrackingInterval = null;

onMounted(() => {
    
  // timeTrackingInterval = setInterval(() => {
  //   if (state.videoPlayer && state.videoPlayer.$refs.video && ttsStore.currentSrtList?.items.length > 0 && !state.videoPlayer.$refs.video.paused) {
  //     updateActiveSubtitle(state.videoPlayer.$refs.video.currentTime);
  //   }
  // }, 100);
});

onBeforeUnmount(() => {
  // Clear interval
  if (timeTrackingInterval) {
    clearInterval(timeTrackingInterval);
  }
  
});




function handleVideoTimeUpdate(time) {
  const currentSubtitle = ttsStore.currentSrtList?.items.find(item => item.startTime <= time && item.endTime >= time);
  
  if (currentSubtitle && currentSubtitle.id !== state.currentPlayingSubtitleId) {
    // console.log('handleVideoTimeUpdate', time, currentSubtitle);
    
    // Đánh dấu rằng đây là thay đổi từ video, không phải manual
    isManualSeek.value = true;
    
    // Cập nhật state
    state.currentPlayingSubtitleId = currentSubtitle.id;
    activeSubtitleId.value = currentSubtitle.id;
  }
}




// Convert time from "00:00:00,000" format to seconds
const timeToSeconds = (timeString) => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
};
</script>