<template>
  <div class="video-settings p-4 space-y-6 overflow-auto h-full">
    <!-- Project Settings -->
    <div class="settings-section">
      <h3 class="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
        Project Settings
      </h3>
      
      <div class="space-y-3">
        <!-- Project Name -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Project Name</label>
          <a-input
            v-model:value="projectSettings.name"
            placeholder="Enter project name..."
            size="small"
          />
        </div>
        
        <!-- Video Resolution -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Video Resolution</label>
          <a-select
            v-model:value="projectSettings.resolution"
            size="small"
            class="w-full"
          >
            <a-select-option value="1920x1080">1920x1080 (1080p)</a-select-option>
            <a-select-option value="1280x720">1280x720 (720p)</a-select-option>
            <a-select-option value="3840x2160">3840x2160 (4K)</a-select-option>
            <a-select-option value="2560x1440">2560x1440 (1440p)</a-select-option>
          </a-select>
        </div>
        
        <!-- Frame Rate -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Frame Rate</label>
          <a-select
            v-model:value="projectSettings.fps"
            size="small"
            class="w-full"
          >
            <a-select-option :value="24">24 fps</a-select-option>
            <a-select-option :value="30">30 fps</a-select-option>
            <a-select-option :value="60">60 fps</a-select-option>
          </a-select>
        </div>
      </div>
    </div>
    
    <!-- Preview Settings -->
    <div class="settings-section">
      <h3 class="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="23 7 16 12 23 17 23 7"/>
          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
        </svg>
        Preview Settings
      </h3>
      
      <div class="space-y-3">
        <!-- Preview Quality -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Preview Quality</label>
          <a-select
            v-model:value="previewSettings.quality"
            size="small"
            class="w-full"
          >
            <a-select-option value="low">Low (Fast)</a-select-option>
            <a-select-option value="medium">Medium</a-select-option>
            <a-select-option value="high">High (Slow)</a-select-option>
          </a-select>
        </div>
        
        <!-- Auto-play -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Auto-play on load</label>
          <a-switch
            v-model:checked="previewSettings.autoPlay"
            size="small"
          />
        </div>
        
        <!-- Show Grid -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Show grid by default</label>
          <a-switch
            v-model:checked="previewSettings.showGrid"
            size="small"
          />
        </div>
        
        <!-- Show Safe Area -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Show safe area</label>
          <a-switch
            v-model:checked="previewSettings.showSafeArea"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <!-- Export Settings -->
    <div class="settings-section">
      <h3 class="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
          <polyline points="7,10 12,15 17,10"/>
          <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
        Export Settings
      </h3>
      
      <div class="space-y-3">
        <!-- Output Format -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Output Format</label>
          <a-select
            v-model:value="exportSettings.format"
            size="small"
            class="w-full"
          >
            <a-select-option value="mp4">MP4</a-select-option>
            <a-select-option value="mov">MOV</a-select-option>
            <a-select-option value="avi">AVI</a-select-option>
            <a-select-option value="webm">WebM</a-select-option>
          </a-select>
        </div>
        
        <!-- Video Codec -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Video Codec</label>
          <a-select
            v-model:value="exportSettings.codec"
            size="small"
            class="w-full"
          >
            <a-select-option value="h264">H.264</a-select-option>
            <a-select-option value="h265">H.265 (HEVC)</a-select-option>
            <a-select-option value="vp9">VP9</a-select-option>
          </a-select>
        </div>
        
        <!-- Bitrate -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Video Bitrate</label>
          <a-slider
            v-model:value="exportSettings.bitrate"
            :min="1000"
            :max="20000"
            :step="500"
            size="small"
          />
          <div class="text-xs text-gray-500 mt-1">{{ exportSettings.bitrate }}kbps</div>
        </div>
        
        <!-- Audio Settings -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Audio Bitrate</label>
          <a-select
            v-model:value="exportSettings.audioBitrate"
            size="small"
            class="w-full"
          >
            <a-select-option value="128">128 kbps</a-select-option>
            <a-select-option value="192">192 kbps</a-select-option>
            <a-select-option value="256">256 kbps</a-select-option>
            <a-select-option value="320">320 kbps</a-select-option>
          </a-select>
        </div>
      </div>
    </div>
    
    <!-- Subtitle Settings -->
    <div class="settings-section">
      <h3 class="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
        </svg>
        Subtitle Settings
      </h3>
      
      <div class="space-y-3">
        <!-- Default Font -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Default Font</label>
          <a-select
            v-model:value="subtitleSettings.defaultFont"
            size="small"
            class="w-full"
          >
            <a-select-option value="Arial">Arial</a-select-option>
            <a-select-option value="Helvetica">Helvetica</a-select-option>
            <a-select-option value="Times New Roman">Times New Roman</a-select-option>
            <a-select-option value="Courier New">Courier New</a-select-option>
            <a-select-option value="Verdana">Verdana</a-select-option>
          </a-select>
        </div>
        
        <!-- Default Font Size -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Default Font Size</label>
          <a-slider
            v-model:value="subtitleSettings.defaultFontSize"
            :min="12"
            :max="100"
            size="small"
          />
          <div class="text-xs text-gray-500 mt-1">{{ subtitleSettings.defaultFontSize }}px</div>
        </div>
        
        <!-- Burn Subtitles -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Burn subtitles into video</label>
          <a-switch
            v-model:checked="subtitleSettings.burnSubtitles"
            size="small"
          />
        </div>
        
        <!-- Export SRT -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Export separate SRT file</label>
          <a-switch
            v-model:checked="subtitleSettings.exportSRT"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <!-- Performance Settings -->
    <div class="settings-section">
      <h3 class="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 2v20m8-10H4"/>
        </svg>
        Performance
      </h3>
      
      <div class="space-y-3">
        <!-- Hardware Acceleration -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Hardware acceleration</label>
          <a-switch
            v-model:checked="performanceSettings.hardwareAcceleration"
            size="small"
          />
        </div>
        
        <!-- Memory Usage -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Memory Usage</label>
          <a-select
            v-model:value="performanceSettings.memoryUsage"
            size="small"
            class="w-full"
          >
            <a-select-option value="low">Low (Slower)</a-select-option>
            <a-select-option value="medium">Medium</a-select-option>
            <a-select-option value="high">High (Faster)</a-select-option>
          </a-select>
        </div>
        
        <!-- Auto-save -->
        <div class="flex items-center justify-between">
          <label class="text-xs text-gray-400">Auto-save project</label>
          <a-switch
            v-model:checked="performanceSettings.autoSave"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <!-- Actions -->
    <div class="settings-actions pt-4 border-t border-gray-600">
      <div class="space-y-2">
        <a-button block size="small" @click="resetSettings">
          Reset to Defaults
        </a-button>
        
        <a-button type="primary" block size="small" @click="saveSettings">
          Save Settings
        </a-button>
        
        <a-button block size="small" @click="exportProject">
          Export Project
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { message } from 'ant-design-vue'

const layersStore = useVideoLayersStore()

// Settings state
const projectSettings = reactive({
  name: 'Untitled Project',
  resolution: '1920x1080',
  fps: 30
})

const previewSettings = reactive({
  quality: 'medium',
  autoPlay: false,
  showGrid: false,
  showSafeArea: false
})

const exportSettings = reactive({
  format: 'mp4',
  codec: 'h264',
  bitrate: 5000,
  audioBitrate: '128'
})

const subtitleSettings = reactive({
  defaultFont: 'Arial',
  defaultFontSize: 48,
  burnSubtitles: true,
  exportSRT: true
})

const performanceSettings = reactive({
  hardwareAcceleration: true,
  memoryUsage: 'medium',
  autoSave: true
})

// Methods
const resetSettings = () => {
  Object.assign(projectSettings, {
    name: 'Untitled Project',
    resolution: '1920x1080',
    fps: 30
  })
  
  Object.assign(previewSettings, {
    quality: 'medium',
    autoPlay: false,
    showGrid: false,
    showSafeArea: false
  })
  
  Object.assign(exportSettings, {
    format: 'mp4',
    codec: 'h264',
    bitrate: 5000,
    audioBitrate: '128'
  })
  
  Object.assign(subtitleSettings, {
    defaultFont: 'Arial',
    defaultFontSize: 48,
    burnSubtitles: true,
    exportSRT: true
  })
  
  Object.assign(performanceSettings, {
    hardwareAcceleration: true,
    memoryUsage: 'medium',
    autoSave: true
  })
  
  message.success('Settings reset to defaults')
}

const saveSettings = () => {
  // Save to localStorage or backend
  const settings = {
    project: projectSettings,
    preview: previewSettings,
    export: exportSettings,
    subtitle: subtitleSettings,
    performance: performanceSettings
  }
  
  localStorage.setItem('videoEditorSettings', JSON.stringify(settings))
  
  // Update layers store with export settings
  layersStore.updateExportSettings(exportSettings)
  
  message.success('Settings saved successfully')
}

const exportProject = () => {
  // TODO: Implement project export
  const projectData = {
    settings: {
      project: projectSettings,
      preview: previewSettings,
      export: exportSettings,
      subtitle: subtitleSettings,
      performance: performanceSettings
    },
    layers: layersStore.layers,
    effects: layersStore.effects,
    videoInfo: layersStore.videoInfo
  }
  
  const dataStr = JSON.stringify(projectData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `${projectSettings.name}.json`
  link.click()
  
  URL.revokeObjectURL(url)
  message.success('Project exported successfully')
}

// Load settings on mount
const loadSettings = () => {
  try {
    const saved = localStorage.getItem('videoEditorSettings')
    if (saved) {
      const settings = JSON.parse(saved)
      
      if (settings.project) Object.assign(projectSettings, settings.project)
      if (settings.preview) Object.assign(previewSettings, settings.preview)
      if (settings.export) Object.assign(exportSettings, settings.export)
      if (settings.subtitle) Object.assign(subtitleSettings, settings.subtitle)
      if (settings.performance) Object.assign(performanceSettings, settings.performance)
    }
  } catch (error) {
    console.error('Failed to load settings:', error)
  }
}

// Watch for changes and auto-save if enabled
watch([projectSettings, previewSettings, exportSettings, subtitleSettings, performanceSettings], () => {
  if (performanceSettings.autoSave) {
    saveSettings()
  }
}, { deep: true })

// Load settings on component mount
loadSettings()
</script>

<style scoped>
.video-settings {
  font-size: 12px;
}

.settings-section {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.settings-section h3 {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.video-settings :deep(.ant-slider) {
  margin: 0;
}

.video-settings :deep(.ant-select) {
  width: 100%;
}

.video-settings :deep(.ant-input) {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.5);
  color: white;
}

.video-settings :deep(.ant-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
</style>
