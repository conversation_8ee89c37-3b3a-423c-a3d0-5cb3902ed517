<template>
  <div class="effect-overlay absolute inset-0 pointer-events-none">
    <!-- Blur Effect -->
    <div
      v-if="effect.type === 'blur' && isVisible"
      class="blur-effect absolute border-2 border-blue-500 border-dashed"
      :style="blurStyle"
    >
      <div class="absolute inset-0 bg-blue-500 bg-opacity-20 backdrop-blur-sm">
        <div class="absolute top-1 left-1 text-xs text-white bg-blue-500 px-1 rounded">
          BLUR
        </div>
      </div>
    </div>
    
    <!-- Delogo Effect -->
    <div
      v-if="effect.type === 'delogo' && isVisible"
      class="delogo-effect absolute border-2 border-red-500 border-dashed"
      :style="delogoStyle"
    >
      <div class="absolute inset-0 bg-red-500 bg-opacity-20">
        <div class="absolute top-1 left-1 text-xs text-white bg-red-500 px-1 rounded">
          DELOGO
        </div>
        <!-- Cross pattern to indicate removal -->
        <div class="absolute inset-0 flex items-center justify-center">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Color Correction Effect -->
    <div
      v-if="effect.type === 'color' && isVisible"
      class="color-effect absolute border-2 border-green-500 border-dashed"
      :style="colorStyle"
    >
      <div class="absolute inset-0 bg-green-500 bg-opacity-20">
        <div class="absolute top-1 left-1 text-xs text-white bg-green-500 px-1 rounded">
          COLOR
        </div>
      </div>
    </div>
    
    <!-- Crop Effect -->
    <div
      v-if="effect.type === 'crop' && isVisible"
      class="crop-effect absolute border-2 border-yellow-500 border-dashed"
      :style="cropStyle"
    >
      <div class="absolute inset-0 bg-yellow-500 bg-opacity-20">
        <div class="absolute top-1 left-1 text-xs text-white bg-yellow-500 px-1 rounded">
          CROP
        </div>
        <!-- Corner handles -->
        <div class="absolute -top-1 -left-1 w-2 h-2 bg-yellow-500"></div>
        <div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500"></div>
        <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-yellow-500"></div>
        <div class="absolute -bottom-1 -right-1 w-2 h-2 bg-yellow-500"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  effect: {
    type: Object,
    required: true
  },
  currentTime: {
    type: Number,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  }
})

// Check if effect should be visible at current time
const isVisible = computed(() => {
  return props.currentTime >= props.effect.timeStart && props.currentTime <= props.effect.timeEnd
})

// Convert effect coordinates to video coordinates
const getEffectStyle = (effect) => {
  const { width, height } = props.videoDimensions
  
  // Convert percentage or pixel coordinates to actual video coordinates
  let x, y, w, h
  
  if (effect.x <= 1 && effect.y <= 1) {
    // Normalized coordinates (0-1)
    x = effect.x * width
    y = effect.y * height
    w = effect.width * width
    h = effect.height * height
  } else {
    // Pixel coordinates
    x = effect.x
    y = effect.y
    w = effect.width
    h = effect.height
  }
  
  return {
    left: x + 'px',
    top: y + 'px',
    width: w + 'px',
    height: h + 'px'
  }
}

// Blur effect styling
const blurStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }
  return getEffectStyle(props.effect)
})

// Delogo effect styling
const delogoStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }
  return getEffectStyle(props.effect)
})

// Color correction effect styling
const colorStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }
  return getEffectStyle(props.effect)
})

// Crop effect styling
const cropStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }
  return getEffectStyle(props.effect)
})
</script>

<style scoped>
.effect-overlay {
  z-index: 30;
}

.blur-effect,
.delogo-effect,
.color-effect,
.crop-effect {
  transition: all 0.2s ease;
}

.blur-effect:hover,
.delogo-effect:hover,
.color-effect:hover,
.crop-effect:hover {
  border-width: 3px;
}
</style>
