import { defineStore } from 'pinia';
import { useTTSStore } from './ttsStore';

export const useTimelineStore = defineStore('timeline', {
  state: () => ({
    // Timeline view settings
    zoom: 0.5, // Zoom level (1 = 1 second = 100px)
    scrollLeft: 0, // Horizontal scroll position
    duration: 0, // Total timeline duration in seconds

    // Playhead
    currentTime: 0, // Current playhead position in seconds
    isPlaying: false,

    // Selection
    selectedItems: [], // Array of selected subtitle IDs

    // Drag & Drop
    isDragging: false,
    dragType: null, // 'move', 'resize-left', 'resize-right'
    dragStartX: 0,
    dragStartTime: 0,
    dragItem: null,

    // Grid and snapping
    snapToGrid: true,
    gridSize: 0.1, // Grid size in seconds (100ms)
    snapDistance: 5, // Snap distance in pixels

    // Timeline dimensions
    trackHeight: 60, // Height of each track in pixels
    rulerHeight: 40, // Height of ruler in pixels
    timelineWidth: 0, // Total timeline width in pixels

    // View settings
    showWaveform: false,
    showThumbnails: false,

    // Undo/Redo
    history: [],
    historyIndex: -1,
    maxHistorySize: 50,
  }),

  getters: {
    // Convert time to pixel position
    timeToPixel: (state) => (time) => {
      return time * state.zoom * 100; // 100px per second at zoom level 1
    },

    // Convert pixel position to time
    pixelToTime: (state) => (pixel) => {
      return pixel / (state.zoom * 100);
    },

    // Get visible time range
    visibleTimeRange: (state) => {
      if (!state.timelineWidth || state.zoom <= 0) {
        return { start: 0, end: state.duration };
      }
      const startTime = state.scrollLeft / (state.zoom * 100);
      const endTime = startTime + (state.timelineWidth / (state.zoom * 100));
      return { start: Math.max(0, startTime), end: Math.min(state.duration, endTime) };
    },

    // Get timeline width in pixels
    totalTimelineWidth: (state) => {
      return state.duration * state.zoom * 100;
    },

    // Get current subtitle items from TTS store
    subtitleItems: () => {
      const ttsStore = useTTSStore();
      return ttsStore.currentSrtList?.items || [];
    },

    // Check if item is selected
    isItemSelected: (state) => (itemId) => {
      return state.selectedItems.includes(itemId);
    },

    // Get snap position for time
    getSnapTime: (state) => (time) => {
      if (!state.snapToGrid) return time;
      return Math.round(time / state.gridSize) * state.gridSize;
    },
  },

  actions: {
    // Set timeline duration
    setDuration(duration) {
      this.duration = duration;
    },

    // Set current time (playhead position)
    setCurrentTime(time) {
      this.currentTime = Math.max(0, Math.min(time, this.duration));
    },

    // Set zoom level
    setZoom(zoom) {
      const minZoom = 0.1;
      const maxZoom = 10;
      this.zoom = Math.max(minZoom, Math.min(zoom, maxZoom));
    },

    // Zoom in/out
    zoomIn() {
      this.setZoom(this.zoom * 1.2);
    },

    zoomOut() {
      this.setZoom(this.zoom / 1.2);
    },

    // Fit timeline to view
    fitToView() {
      if (this.duration > 0 && this.timelineWidth > 0) {
        this.setZoom((this.timelineWidth - 40) / (this.duration * 100));
        this.scrollLeft = 0;
      }
    },

    // Set scroll position
    setScrollLeft(scrollLeft) {
      this.scrollLeft = Math.max(0, scrollLeft);
    },

    // Selection methods
    selectItem(itemId) {
      if (!this.selectedItems.includes(itemId)) {
        this.selectedItems.push(itemId);
      }
    },

    deselectItem(itemId) {
      const index = this.selectedItems.indexOf(itemId);
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      }
    },

    selectMultiple(itemIds) {
      this.selectedItems = [...new Set([...this.selectedItems, ...itemIds])];
    },

    clearSelection() {
      this.selectedItems = [];
    },

    toggleSelection(itemId) {
      if (this.isItemSelected(itemId)) {
        this.deselectItem(itemId);
      } else {
        this.selectItem(itemId);
      }
    },

    // Drag & Drop methods
    startDrag(type, item, startX, startTime) {
      this.isDragging = true;
      this.dragType = type;
      this.dragItem = item;
      this.dragStartX = startX;
      this.dragStartTime = startTime;
    },

    stopDrag() {
      this.isDragging = false;
      this.dragType = null;
      this.dragItem = null;
      this.dragStartX = 0;
      this.dragStartTime = 0;
    },

    // Timeline dimensions
    setTimelineWidth(width) {
      this.timelineWidth = width;
    },

    // History methods
    saveState() {
      const ttsStore = useTTSStore();
      if (!ttsStore.currentSrtList) return;

      const state = {
        items: JSON.parse(JSON.stringify(ttsStore.currentSrtList.items)),
        timestamp: Date.now()
      };

      // Remove future history if we're not at the end
      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1);
      }

      this.history.push(state);

      // Limit history size
      if (this.history.length > this.maxHistorySize) {
        this.history.shift();
      } else {
        this.historyIndex++;
      }
    },

    undo() {
      if (this.historyIndex > 0) {
        this.historyIndex--;
        const state = this.history[this.historyIndex];
        const ttsStore = useTTSStore();
        if (ttsStore.currentSrtList) {
          ttsStore.currentSrtList.items = JSON.parse(JSON.stringify(state.items));
        }
      }
    },

    redo() {
      if (this.historyIndex < this.history.length - 1) {
        this.historyIndex++;
        const state = this.history[this.historyIndex];
        const ttsStore = useTTSStore();
        if (ttsStore.currentSrtList) {
          ttsStore.currentSrtList.items = JSON.parse(JSON.stringify(state.items));
        }
      }
    },

    // Toggle settings
    toggleSnapToGrid() {
      this.snapToGrid = !this.snapToGrid;
    },

    toggleWaveform() {
      this.showWaveform = !this.showWaveform;
    },

    toggleThumbnails() {
      this.showThumbnails = !this.showThumbnails;
    },
  }
});
