<template>
  <div class="video-player-container">
    <!-- Video Player with Crop Selector -->
    <CropSelector ref="cropSelector" :active="cropMode" :content-width="videoDimensions.width"
      :content-height="videoDimensions.height" :show-coordinates="true" @crop-selected="onCropSelected"
      @crop-change="onCropChange" @crop-cleared="onCropCleared">
      <div class="video-player" :class="videoClass">
        <video ref="video" :src="src" @loadedmetadata="handleVideoLoaded" @timeupdate="handleTimeUpdate"
          @click="handleVideoClick"></video>
      </div>
    </CropSelector>

    <!-- Custom video controls -->
    <div class="mt-2 p-1 bg-gray-900 rounded-md">
      <div class="flex items-center space-x-2">
        <!-- Play/Pause button -->
        <button @click="togglePlayPause" class="px-2 py-1 bg-gray-900 hover:bg-gray-800 rounded">
          {{ isPlaying ? '⏸️' : '▶️' }}
        </button>

        <!-- Progress bar container -->
        <div class="flex-grow h-2 bg-gray-500 rounded-full overflow-hidden cursor-pointer relative"
          ref="progressContainer" @click="seekVideo" @mousedown="startDragging" @mousemove="onDrag"
          @mouseup="stopDragging" @mouseleave="stopDragging">
          <div class="h-full bg-blue-500 pointer-events-none" :style="{ width: `${videoProgress}%` }"></div>
          <!-- Draggable handle -->
          <div class="absolute top-0 w-3 h-2 bg-white rounded-full transform -translate-x-1/2 cursor-pointer"
            :style="{ left: `${videoProgress}%` }" @mousedown.stop="startDragging"></div>
        </div>

        <!-- Time display -->
        <div class="text-xs text-gray-600">
          {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
        </div>

        <!-- Volume control -->
        <div class="flex items-center space-x-1">
          <button @click="toggleMute" class="px-1 py-1 bg-gray-900 hover:bg-gray-800 rounded">
            {{ isMuted ? '🔇' : '🔊' }}
          </button>
          <input type="range" min="0" max="1" step="0.1" v-model="volume" class="w-16" />
        </div>

        <!-- Crop Controls -->
        <div class="flex items-center space-x-1 border-l border-gray-600 pl-2">
          <button @click="toggleCropMode" class="px-2 py-1 rounded text-xs"
            :class="cropMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'"
            title="Toggle crop selection mode">
            🎯
          </button>

          <button v-if="currentCrop && (currentCrop.width > 0 || currentCrop.height > 0)" @click="extractText"
            class="px-2 py-1 bg-green-600 hover:bg-green-500 text-white rounded text-xs"
            title="Extract text from selected area">
            OCR
          </button>

          <button v-if="currentCrop && (currentCrop.width > 0 || currentCrop.height > 0)" @click="clearCrop"
            class="px-1 py-1 bg-red-600 hover:bg-red-500 text-white rounded text-xs" title="Clear selection">
            ✕
          </button>
        </div>
      </div>

      <!-- Crop Info Display -->
      <div v-if="currentCrop && currentCrop.width > 0" class="mt-2 text-xs text-gray-400">
        <div class="flex justify-between items-center">
          <span>
            Crop: {{ Math.round(currentCrop.x) }}, {{ Math.round(currentCrop.y) }},
            {{ Math.round(currentCrop.width) }} × {{ Math.round(currentCrop.height) }}
          </span>
          <span v-if="state.cropText" class="text-green-400" @click="copyText">
            Text: {{ state.cropText }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { state } from '@/lib/state';
import { useTTSStore } from '@/stores/ttsStore';

export default {
  name: 'VideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: '48'
    },
    // Enable crop functionality
    enableCrop: {
      type: Boolean,
      default: true
    },
    clickPlay: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1,
      isMuted: false,
      videoProgress: 0,
      videoDimensions: {
        width: 0,
        height: 0
      },
      isDragging: false,
      wasPlayingBeforeDrag: false,

      // Crop related
      cropMode: false,
      currentCrop: null,
      extractedText: ''
    };
  },
  setup() {
    const ttsStore = useTTSStore();
    return {
      state,
      ttsStore
    };
  },
  mounted() {
    this.$refs.video.addEventListener('timeupdate', this.handleTimeUpdate);
    // Add global event listeners for dragging
    document.addEventListener('mousemove', this.onGlobalDrag);
    document.addEventListener('mouseup', this.onGlobalMouseUp);
  },
  computed: {
    videoClass() {
      return `w-${this.size}`;
    }
  },
  watch: {
    volume(newVolume) {
      if (this.$refs.video) {
        this.$refs.video.volume = newVolume;
      }
    }
  },
  beforeUnmount() {
    this.$refs.video.removeEventListener('timeupdate', this.handleTimeUpdate);
    document.removeEventListener('mousemove', this.onGlobalDrag);
    document.removeEventListener('mouseup', this.onGlobalMouseUp);
  },
  methods: {
    handleTimeUpdate() {
      if (!this.isDragging) {
        this.$emit('timeupdate', this.$refs.video.currentTime);
        this.currentTime = this.$refs.video.currentTime;
        state.currentTime = this.currentTime;
        this.videoProgress = this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0;
      }
    },
    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00';

      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },
    handleVideoLoaded(event) {
      this.$emit('loadedmetadata', event);
      if (this.$refs.video) {
        // Store video dimensions for crop calculations
        this.videoDimensions = {
          width: this.$refs.video.videoWidth,
          height: this.$refs.video.videoHeight
        };

        // Set video duration
        this.duration = this.$refs.video.duration;

        // Reset video player state
        this.currentTime = 0;
        this.isPlaying = false;
        this.videoProgress = 0;

        // Set default volume
        this.$refs.video.volume = this.volume;
      }
    },
    handleVideoClick() {
      if (!this.cropMode && this.clickPlay) {
        this.togglePlayPause();
      }
    },
    togglePlayPause() {
      if (!this.$refs.video) return;

      if (this.isPlaying) {
        this.$refs.video.pause();
      } else {
        this.$refs.video.play();
      }

      this.isPlaying = !this.isPlaying;
    },
    toggleMute() {
      if (!this.$refs.video) return;

      this.$refs.video.muted = !this.$refs.video.muted;
      this.isMuted = this.$refs.video.muted;
    },
    seekVideo(event) {
      if (!this.$refs.video || !this.$refs.progressContainer || this.duration === 0) return;

      // Get the progress container element (not the clicked element)
      const progressContainer = this.$refs.progressContainer;
      const rect = progressContainer.getBoundingClientRect();

      // Calculate position relative to the progress container
      const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
      const newTime = pos * this.duration;

      // Update video time
      this.$refs.video.currentTime = newTime;
      this.currentTime = newTime;
      this.videoProgress = pos * 100;
    },
    startDragging(event) {
      event.preventDefault();
      this.isDragging = true;
      this.wasPlayingBeforeDrag = this.isPlaying;

      // Pause video while dragging for smoother experience
      if (this.isPlaying) {
        this.$refs.video.pause();
      }

      // Immediately update position
      this.updateDragPosition(event);
    },
    onDrag(event) {
      if (this.isDragging) {
        this.updateDragPosition(event);
      }
    },
    onGlobalDrag(event) {
      if (this.isDragging) {
        this.updateDragPosition(event);
      }
    },
    onGlobalMouseUp() {
      if (this.isDragging) {
        this.stopDragging();
      }
    },
    stopDragging() {
      if (!this.isDragging) return;

      this.isDragging = false;

      // Resume playing if it was playing before drag
      if (this.wasPlayingBeforeDrag) {
        this.$refs.video.play();
        this.isPlaying = true;
      }
    },
    updateDragPosition(event) {
      if (!this.$refs.video || !this.$refs.progressContainer || this.duration === 0) return;

      const rect = this.$refs.progressContainer.getBoundingClientRect();
      const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
      const newTime = pos * this.duration;

      // Update video time and progress
      this.$refs.video.currentTime = newTime;
      this.currentTime = newTime;
      this.videoProgress = pos * 100;
    },

    // Crop related methods
    toggleCropMode() {
      this.cropMode = !this.cropMode;
      if (this.cropMode) {
        this.$refs.cropSelector.activate();
      } else {
        this.$refs.cropSelector.deactivate();
      }
    },
    onCropSelected(cropData) {
      this.currentCrop = cropData.normalized;
      this.$emit('crop-selected', {
        ...cropData,
        timestamp: this.currentTime,
        videoDimensions: this.videoDimensions
      });
    },
    onCropChange(cropData) {
      this.currentCrop = cropData.normalized;
      this.$emit('crop-change', {
        ...cropData,
        timestamp: this.currentTime,
        videoDimensions: this.videoDimensions
      });
    },
    onCropCleared() {
      this.currentCrop = null;
      this.extractedText = '';
      this.$emit('crop-cleared');
    },
    clearCrop() {
      this.$refs.cropSelector.clearSelection();
    },
    async extractText() {
      if (!this.currentCrop || !this.currentCrop.width || !this.currentCrop.height) {
        console.warn('No crop area selected');
        return;
      }

      try {
        // Pause video for stable frame capture
        const wasPlaying = this.isPlaying;
        if (wasPlaying) {
          this.$refs.video.pause();
          this.isPlaying = false;
        }

        // Create canvas to capture video frame
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to video dimensions
        canvas.width = this.videoDimensions.width;
        canvas.height = this.videoDimensions.height;

        // Draw current video frame
        ctx.drawImage(this.$refs.video, 0, 0, canvas.width, canvas.height);

        // Extract cropped region
        const cropCanvas = document.createElement('canvas');
        const cropCtx = cropCanvas.getContext('2d');

        cropCanvas.width = this.currentCrop.width;
        cropCanvas.height = this.currentCrop.height;

        cropCtx.drawImage(
          canvas,
          this.currentCrop.x, this.currentCrop.y, this.currentCrop.width, this.currentCrop.height,
          0, 0, this.currentCrop.width, this.currentCrop.height
        );

        // Convert to blob for OCR processing
        const blob = await new Promise(resolve => cropCanvas.toBlob(resolve, 'image/png'));

        // Emit OCR request with crop data
        this.$emit('ocr-request', {
          imageBlob: blob,
          cropData: {
            ...this.currentCrop,
            timestamp: this.currentTime,
            videoDimensions: this.videoDimensions
          }
        });

        // Resume playing if it was playing before
        if (wasPlaying) {
          this.$refs.video.play();
          this.isPlaying = true;
        }

        // For demo purposes - you would replace this with actual OCR API call
        this.simulateOCR(blob);

      } catch (error) {
        console.error('Error extracting text:', error);
        this.$emit('ocr-error', error);
      }
    },

    // Demo OCR simulation - replace with actual PaddleOCR integration
    async simulateOCR(imageBlob) {
      // Simulate API delay
      // await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock OCR result
      // this.extractedText = `Sample text from crop area at ${this.formatTime(this.currentTime)}`;
      // console.log(this.extractedText);
      // console.log(this.currentCrop);
      // console.log(this.videoDimensions);
      // console.log(this.currentTime);

      // this.$emit('ocr-result', {
      //   text: this.extractedText,
      //   cropData: this.currentCrop,
      //   timestamp: this.currentTime,
      //   videoDimensions: this.videoDimensions,
      //   confidence: 0.95
      // });
      const x1 = this.currentCrop.x / this.videoDimensions.width;
      const y1 = this.currentCrop.y / this.videoDimensions.height;
      const x2 = (this.currentCrop.x + this.currentCrop.width) / this.videoDimensions.width;
      const y2 = (this.currentCrop.y + this.currentCrop.height) / this.videoDimensions.height;
      const normalized = [x1, y1, x2, y2];
      const rounded = normalized.map(num => Math.round(num * 100) / 100);
      console.log(rounded);
      state.cropData = rounded
      message.info('Đang lấy đoạn text từ frame video', 5);
      const videoPath = this.ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')
      const cloned = JSON.parse(JSON.stringify({
        videoPath,
        fromSecond: this.currentTime,
        cropData: rounded
      }));
      const res = await electronAPI.getTextFromFrameVideo(cloned)
        .catch(err => {
          console.error('Error getting text from frame video:', err);
          message.error('Error getting text from frame video: ' + err.message, 5);
        });
        console.log('res', res);
      if (res?.text) {
        message.success('Lấy đoạn text thành công', 5);
        state.cropText = res.text;
        this.extractedText = res.text;
      }
    },
    copyText() {
      if (!this.extractedText) return;

      navigator.clipboard.writeText(this.extractedText)
        .then(() => {
          message.success('Text copied to clipboard', 5);
        })
        .catch(err => {
          console.error('Error copying text:', err);
          message.error('Error copying text', 5);
        });
    },
    // Public methods for external control
    setCropArea(normalizedCrop) {
      if (this.$refs.cropSelector) {
        this.$refs.cropSelector.setCropFromNormalized(normalizedCrop);
      }
    },

    getCurrentCrop() {
      return this.$refs.cropSelector ? this.$refs.cropSelector.getCropData() : null;
    },

    enableCropMode() {
      this.cropMode = true;
      this.$refs.cropSelector.activate();
    },

    disableCropMode() {
      this.cropMode = false;
      this.$refs.cropSelector.deactivate();
    }
  }
};
</script>

<style scoped>
.video-player-container {
  display: inline-block;
}

.video-player {
  margin: 0 auto;
  position: relative;
}

/* Ensure smooth dragging */
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:active {
  cursor: grabbing;
}

/* Crop mode styling */
.video-player video {
  display: block;
}
</style>