<template>
  <div class="right-panel flex-1 bg-gray-800 text-white flex flex-col h-full">
    <!-- Tab Header -->
    <div class="tab-header flex items-center bg-gray-900 border-b border-gray-600">
      <div class="flex-1 flex">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          :class="[
            'px-4 py-2 text-sm font-medium transition-colors duration-200 border-b-2',
            activeTab === tab.key
              ? 'text-blue-400 border-blue-400 bg-gray-800'
              : 'text-gray-400 border-transparent hover:text-gray-200 hover:bg-gray-700'
          ]"
        >
          <div class="flex items-center gap-2">
            <component :is="tab.icon" class="w-4 h-4" />
            <span>{{ tab.label }}</span>
          </div>
        </button>
      </div>
      
      <!-- Tab Actions -->
      <div class="flex items-center gap-2 px-3">
        <!-- Minimize/Maximize -->
        <button
          @click="isMinimized = !isMinimized"
          class="p-1 hover:bg-gray-700 rounded transition-colors"
          :title="isMinimized ? 'Expand' : 'Minimize'"
        >
          <ChevronDown v-if="!isMinimized" class="w-4 h-4" />
          <ChevronUp v-if="isMinimized" class="w-4 h-4" />
        </button>
      </div>
    </div>
    
    <!-- Tab Content -->
    <div v-if="!isMinimized" class="tab-content flex-1 overflow-hidden">
      <!-- Translator Tab -->
      <div v-if="activeTab === 'translator'" class="h-full">
        <SubtitleTranslatorTerm />
      </div>
      
      <!-- Layers Tab -->
      <div v-if="activeTab === 'layers'" class="h-full">
        <VideoLayersPanel />
      </div>
      
      <!-- Effects Tab -->
      <div v-if="activeTab === 'effects'" class="h-full">
        <EffectsPanel />
      </div>
      
      <!-- Settings Tab -->
      <div v-if="activeTab === 'settings'" class="h-full p-4">
        <VideoSettings />
      </div>
    </div>
    
    <!-- Minimized State -->
    <div v-if="isMinimized" class="minimized-content p-2 bg-gray-800">
      <div class="flex items-center justify-between text-xs text-gray-400">
        <span>{{ tabs.find(t => t.key === activeTab)?.label }}</span>
        <span v-if="activeTab === 'translator' && ttsStore.currentSrtList">
          {{ ttsStore.currentSrtList.items?.length || 0 }} items
        </span>
        <span v-if="activeTab === 'layers'">
          {{ layersStore.enabledLayers.length }} layers
        </span>
        <span v-if="activeTab === 'effects'">
          {{ activeEffectsCount }} effects
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'
import { useTTSStore } from '@/stores/ttsStore'
import { useVideoLayersStore } from '@/stores/video-layers-store'

// Components
import SubtitleTranslatorTerm from '../SubtitleTranslatorTerm.vue'
import VideoLayersPanel from './VideoLayersPanel.vue'
import EffectsPanel from './EffectsPanel.vue'
import VideoSettings from './VideoSettings.vue'

// Stores
const ttsStore = useTTSStore()
const layersStore = useVideoLayersStore()

// Local state
const activeTab = ref('translator')
const isMinimized = ref(false)

// Tab configuration
const tabs = [
  {
    key: 'translator',
    label: 'Translator',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      h('path', { d: 'M5 8l6 6' }),
      h('path', { d: 'M4 14l6-6 2-3' }),
      h('path', { d: 'M2 5h12' }),
      h('path', { d: 'M7 2h1' }),
      h('path', { d: 'M22 22l-5-10-5 10' }),
      h('path', { d: 'M14 18h6' })
    ])
  },
  {
    key: 'layers',
    label: 'Layers',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      h('polygon', { points: '12,2 2,7 12,12 22,7 12,2' }),
      h('polyline', { points: '2,17 12,22 22,17' }),
      h('polyline', { points: '2,12 12,17 22,12' })
    ])
  },
  {
    key: 'effects',
    label: 'Effects',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      h('circle', { cx: 12, cy: 12, r: 3 }),
      h('path', { d: 'M12 1v6m0 6v6m11-7h-6m-6 0H1' })
    ])
  },
  {
    key: 'settings',
    label: 'Settings',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      h('circle', { cx: 12, cy: 12, r: 3 }),
      h('path', { d: 'M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z' })
    ])
  }
]

// Computed
const activeEffectsCount = computed(() => {
  const effects = layersStore.activeEffects
  return effects.delogo.length + effects.blur.length + effects.text.length + effects.image.length
})
</script>

<style scoped>
.right-panel {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tab-header {
  min-height: 48px;
}

.tab-content {
  background: linear-gradient(to bottom, #1f2937 0%, #111827 100%);
}

.minimized-content {
  min-height: 32px;
}

/* Custom scrollbar */
.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: #1f2937;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
</style>
