<template>
  <div
    class="timeline-ruler bg-gray-800 border-b border-gray-600 relative overflow-hidden"
    :style="{ height: timelineStore.rulerHeight + 'px' }"
  >
    <!-- Ruler Background -->
    <div
      class="ruler-content relative h-full overflow-hidden"
      :style="{
        width: '100%'
      }"
    >
      <!-- Ruler marks container -->
      <div
        class="ruler-marks absolute inset-0"
        :style="{
          width: timelineStore.totalTimelineWidth + 'px',
          transform: `translateX(-${timelineStore.scrollLeft}px)`
        }"
      >
      <!-- Major tick marks and labels -->
      <div
        v-for="tick in majorTicks"
        :key="`major-${tick.time}`"
        class="absolute top-0 h-full border-l border-gray-500"
        :style="{ left: tick.position + 'px' }"
      >
        <!-- Time label -->
        <div class="absolute top-1 left-1 text-xs text-gray-300 font-mono">
          {{ formatTime(tick.time) }}
        </div>
      </div>

      <!-- Minor tick marks -->
      <div
        v-for="tick in minorTicks"
        :key="`minor-${tick.time}`"
        class="absolute top-0 border-l border-gray-600"
        :style="{
          left: tick.position + 'px',
          height: tick.height + 'px'
        }"
      ></div>

      <!-- Grid lines for seconds -->
      <div
        v-for="tick in secondTicks"
        :key="`second-${tick.time}`"
        class="absolute top-0 border-l border-gray-700"
        :style="{
          left: tick.position + 'px',
          height: '20px'
        }"
      ></div>
      </div>
    </div>

    <!-- Current time indicator -->
    <div
      class="absolute top-0 w-px bg-red-500 h-full pointer-events-none z-10"
      :style="{
        left: (timelineStore.timeToPixel(timelineStore.currentTime) - timelineStore.scrollLeft) + 'px',
        opacity: timelineStore.currentTime >= 0 ? 1 : 0
      }"
    >
      <!-- Time tooltip -->
      <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
        {{ formatTime(timelineStore.currentTime) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'

const timelineStore = useTimelineStore()

// Computed properties for tick marks
const majorTicks = computed(() => {
  const ticks = []
  const visibleRange = timelineStore.visibleTimeRange
  const zoom = timelineStore.zoom

  // Determine major tick interval based on zoom level
  let majorInterval
  if (zoom >= 5) {
    majorInterval = 1 // 1 second
  } else if (zoom >= 2) {
    majorInterval = 5 // 5 seconds
  } else if (zoom >= 1) {
    majorInterval = 10 // 10 seconds
  } else if (zoom >= 0.5) {
    majorInterval = 30 // 30 seconds
  } else {
    majorInterval = 60 // 1 minute
  }

  // Generate major ticks
  const startTime = Math.floor(visibleRange.start / majorInterval) * majorInterval
  const endTime = Math.ceil(visibleRange.end / majorInterval) * majorInterval

  for (let time = startTime; time <= endTime; time += majorInterval) {
    if (time >= 0 && time <= timelineStore.duration) {
      ticks.push({
        time,
        position: timelineStore.timeToPixel(time)
      })
    }
  }

  return ticks
})

const minorTicks = computed(() => {
  const ticks = []
  const visibleRange = timelineStore.visibleTimeRange
  const zoom = timelineStore.zoom

  // Determine minor tick interval based on zoom level
  let minorInterval, height
  if (zoom >= 5) {
    minorInterval = 0.1 // 100ms
    height = 15
  } else if (zoom >= 2) {
    minorInterval = 0.5 // 500ms
    height = 20
  } else if (zoom >= 1) {
    minorInterval = 1 // 1 second
    height = 25
  } else if (zoom >= 0.5) {
    minorInterval = 5 // 5 seconds
    height = 20
  } else {
    minorInterval = 10 // 10 seconds
    height = 15
  }

  // Generate minor ticks (skip major tick positions)
  const majorInterval = majorTicks.value.length > 1 ?
    majorTicks.value[1].time - majorTicks.value[0].time : 10

  const startTime = Math.floor(visibleRange.start / minorInterval) * minorInterval
  const endTime = Math.ceil(visibleRange.end / minorInterval) * minorInterval

  for (let time = startTime; time <= endTime; time += minorInterval) {
    if (time >= 0 && time <= timelineStore.duration) {
      // Skip if this is a major tick position
      const isMajorTick = time % majorInterval === 0
      if (!isMajorTick) {
        ticks.push({
          time,
          position: timelineStore.timeToPixel(time),
          height
        })
      }
    }
  }

  return ticks
})

const secondTicks = computed(() => {
  const ticks = []
  const visibleRange = timelineStore.visibleTimeRange
  const zoom = timelineStore.zoom

  // Only show second ticks at high zoom levels
  if (zoom < 2) return ticks

  const startTime = Math.floor(visibleRange.start)
  const endTime = Math.ceil(visibleRange.end)

  for (let time = startTime; time <= endTime; time += 1) {
    if (time >= 0 && time <= timelineStore.duration) {
      // Skip if this is a major or minor tick position
      const isMajorTick = majorTicks.value.some(tick => tick.time === time)
      const isMinorTick = minorTicks.value.some(tick => tick.time === time)

      if (!isMajorTick && !isMinorTick) {
        ticks.push({
          time,
          position: timelineStore.timeToPixel(time)
        })
      }
    }
  }

  return ticks
})

// Format time for display
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0:00'

  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }
}
</script>

<style scoped>
.timeline-ruler {
  user-select: none;
  position: relative;
  z-index: 2;
}

.ruler-content {
  background: linear-gradient(to bottom, #374151 0%, #1f2937 100%);
}
</style>
