<template>
  <div class="timeline-editor h-full w-full flex flex-col bg-gray-900 text-white">
    <!-- Timeline Toolbar -->
    <TimelineToolbar />

    <!-- Timeline Content -->
    <div class="timeline-content flex-1 flex flex-col overflow-hidden">
      <!-- Timeline Ruler -->
      <TimelineRuler />

      <!-- Timeline Tracks Container -->
      <div
        ref="timelineContainer"
        class="timeline-tracks flex-1 overflow-auto relative"
        @scroll="handleScroll"
        @wheel="handleWheel"
      >
        <!-- Timeline Track -->
        <TimelineTrack
          @context-menu="handleContextMenu"
          @item-action="handleItemAction"
        />

        <!-- Playhead -->
        <TimelinePlayhead />
      </div>
    </div>

    <!-- Timeline Footer -->
    <div class="timeline-footer flex items-center justify-between p-2 bg-gray-800 border-t border-gray-600 text-xs">
      <div class="flex items-center gap-4">
        <span>Time: {{ formatTime(timelineStore.currentTime) }}</span>
        <span>Duration: {{ formatTime(timelineStore.duration) }}</span>
        <span v-if="timelineStore.selectedItems.length > 0">
          Selected: {{ timelineStore.selectedItems.length }}
        </span>
      </div>

      <div class="flex items-center gap-2">
        <span>Grid: {{ timelineStore.gridSize }}s</span>
        <span>Zoom: {{ Math.round(timelineStore.zoom * 100) }}%</span>
      </div>
    </div>

    <!-- Context Menu -->
    <TimelineContextMenu
      :visible="contextMenu.visible"
      :position="contextMenu.position"
      :click-time="contextMenu.clickTime"
      @close="closeContextMenu"
      @action="handleContextMenuAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { state } from '@/lib/state'
import TimelineToolbar from './TimelineToolbar.vue'
import TimelineRuler from './TimelineRuler.vue'
import TimelineTrack from './TimelineTrack.vue'
import TimelinePlayhead from './TimelinePlayhead.vue'
import TimelineContextMenu from './TimelineContextMenu.vue'

const timelineStore = useTimelineStore()

const timelineContainer = ref(null)

// Context menu state
const contextMenu = ref({
  visible: false,
  position: { x: 0, y: 0 },
  clickTime: 0
})

// Computed
const subtitleItems = computed(() => timelineStore.subtitleItems)

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00.000'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

const handleScroll = (event) => {
  timelineStore.setScrollLeft(event.target.scrollLeft)
}

const handleWheel = (event) => {
  // Ctrl + wheel for zoom
  if (event.ctrlKey) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? -0.1 : 0.1
    timelineStore.setZoom(timelineStore.zoom + delta)
  }
}

const updateTimelineWidth = () => {
  if (timelineContainer.value) {
    timelineStore.setTimelineWidth(timelineContainer.value.clientWidth)
  }
}

const updateDuration = () => {
  if (subtitleItems.value.length > 0) {
    // Calculate duration from last subtitle end time
    const lastSubtitle = subtitleItems.value[subtitleItems.value.length - 1]
    if (lastSubtitle && lastSubtitle.endTime) {
      timelineStore.setDuration(lastSubtitle.endTime + 5) // Add 5 seconds buffer
    }
  } else {
    timelineStore.setDuration(60) // Default 1 minute
  }
}

// Context menu methods
const handleContextMenu = (event, clickTime = 0) => {
  event.preventDefault()

  contextMenu.value = {
    visible: true,
    position: { x: event.clientX, y: event.clientY },
    clickTime: clickTime || timelineStore.pixelToTime(event.offsetX + timelineStore.scrollLeft)
  }

  // Close context menu when clicking elsewhere
  const closeOnClick = (e) => {
    if (!e.target.closest('.timeline-context-menu')) {
      closeContextMenu()
      document.removeEventListener('click', closeOnClick)
    }
  }

  setTimeout(() => {
    document.addEventListener('click', closeOnClick)
  }, 0)
}

const closeContextMenu = () => {
  contextMenu.value.visible = false
}

const handleContextMenuAction = (action, data) => {
  console.log('Context menu action:', action, data)
  // TODO: Implement specific actions
  switch (action) {
    case 'edit':
      // Open edit dialog
      break
    case 'split':
      // Split subtitle
      break
    case 'duplicate':
      // Duplicate subtitle
      break
    case 'merge':
      // Merge subtitles
      break
    case 'delete':
      // Delete subtitles
      break
    case 'insert':
      // Insert new subtitle
      break
    case 'copy':
      // Copy to clipboard
      break
    case 'paste':
      // Paste from clipboard
      break
    case 'generate-audio':
      // Generate audio for single item
      break
    case 'generate-audio-batch':
      // Generate audio for multiple items
      break
    case 'align':
      // Align timing
      break
  }
}

const handleItemAction = (action, data) => {
  console.log('Item action:', action, data)
  // Handle actions from timeline items
}

// Watch for video player time updates
watch(() => state.currentTime, (newTime) => {
  if (newTime !== undefined && newTime !== timelineStore.currentTime) {
    timelineStore.setCurrentTime(newTime)
  }
})

// Watch for timeline current time changes to sync with video
watch(() => timelineStore.currentTime, (newTime) => {
  if (state.videoPlayer?.value?.$refs?.video) {
    const video = state.videoPlayer.value.$refs.video
    if (Math.abs(video.currentTime - newTime) > 0.1) {
      video.currentTime = newTime
    }
  }
})

// Watch for subtitle items changes
watch(subtitleItems, () => {
  updateDuration()
}, { immediate: true, deep: true })

// Lifecycle
onMounted(() => {
  updateTimelineWidth()
  updateDuration()

  // Save initial state
  timelineStore.saveState()

  window.addEventListener('resize', updateTimelineWidth)

  // Keyboard shortcuts
  const handleKeydown = (event) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault()
          if (event.shiftKey) {
            timelineStore.redo()
          } else {
            timelineStore.undo()
          }
          break
        case 'a':
          event.preventDefault()
          // Select all
          const allIds = subtitleItems.value.map(item => item.id)
          timelineStore.selectMultiple(allIds)
          break
      }
    } else {
      switch (event.key) {
        case 'Delete':
        case 'Backspace':
          // Delete selected items
          if (timelineStore.selectedItems.length > 0) {
            // TODO: Implement delete functionality
            console.log('Delete selected items:', timelineStore.selectedItems)
          }
          break
        case 'Escape':
          timelineStore.clearSelection()
          break
      }
    }
  }

  document.addEventListener('keydown', handleKeydown)

  onUnmounted(() => {
    window.removeEventListener('resize', updateTimelineWidth)
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
.timeline-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.timeline-tracks {
  background-image:
    linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 100px 20px;
}

/* Custom scrollbar */
.timeline-tracks::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.timeline-tracks::-webkit-scrollbar-track {
  background: #1f2937;
}

.timeline-tracks::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 6px;
}

.timeline-tracks::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

.timeline-tracks::-webkit-scrollbar-corner {
  background: #1f2937;
}
</style>
