<template>
  <div class="video-preview-editor h-full flex flex-col bg-gray-900">
    <!-- Header Controls -->
    <div class="video-header flex items-center justify-between p-2 bg-gray-800 border-b border-gray-600">
      <div class="flex items-center gap-2">
        <h3 class="text-sm font-medium text-white">Video Preview</h3>
        <span v-if="videoInfo.duration" class="text-xs text-gray-400">
          {{ formatTime(videoInfo.duration) }}
        </span>
      </div>
      
      <!-- View Controls -->
      <div class="flex items-center gap-2">
        <a-button-group size="small">
          <a-button 
            :type="viewMode === 'preview' ? 'primary' : 'default'"
            @click="viewMode = 'preview'"
          >
            Preview
          </a-button>
          <a-button 
            :type="viewMode === 'layers' ? 'primary' : 'default'"
            @click="viewMode = 'layers'"
          >
            Layers
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <!-- Quality -->
        <a-select v-model:value="previewQuality" size="small" style="width: 80px">
          <a-select-option value="low">Low</a-select-option>
          <a-select-option value="medium">Med</a-select-option>
          <a-select-option value="high">High</a-select-option>
        </a-select>
        
        <!-- Zoom -->
        <a-select v-model:value="previewZoom" size="small" style="width: 70px">
          <a-select-option :value="50">50%</a-select-option>
          <a-select-option :value="75">75%</a-select-option>
          <a-select-option :value="100">100%</a-select-option>
          <a-select-option :value="125">125%</a-select-option>
          <a-select-option :value="150">150%</a-select-option>
        </a-select>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="flex-1 flex min-h-0">
      <!-- Video Preview Area -->
      <div class="flex-1 flex flex-col bg-gray-900">
        <!-- Video Container -->
        <div class="flex-1 flex items-center justify-center p-4 relative">
          <div 
            class="video-viewport relative bg-black rounded overflow-hidden"
            :style="viewportStyle"
            @click="handleVideoClick"
          >
            <!-- Video Element -->
            <video
              ref="videoElement"
              class="w-full h-full object-contain"
              :src="videoSrc"
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @play="onPlay"
              @pause="onPause"
              controls
            />
            
            <!-- Overlay Layers -->
            <div class="absolute inset-0 pointer-events-none">
              <!-- Subtitle Layer -->
              <SubtitleOverlay
                v-if="subtitleLayer?.enabled"
                :layer="subtitleLayer"
                :current-time="currentTime"
                :video-dimensions="videoDimensions"
              />
              
              <!-- Text Overlays -->
              <TextOverlay
                v-for="textLayer in enabledTextLayers"
                :key="textLayer.id"
                :layer="textLayer"
                :current-time="currentTime"
                :video-dimensions="videoDimensions"
              />
              
              <!-- Image Overlays -->
              <ImageOverlay
                v-for="imageLayer in enabledImageLayers"
                :key="imageLayer.id"
                :layer="imageLayer"
                :current-time="currentTime"
                :video-dimensions="videoDimensions"
              />
              
              <!-- Effect Overlays -->
              <EffectOverlay
                v-for="effect in activeEffects"
                :key="effect.id"
                :effect="effect"
                :current-time="currentTime"
                :video-dimensions="videoDimensions"
              />
            </div>
            
            <!-- Grid Overlay -->
            <div v-if="showGrid" class="absolute inset-0 pointer-events-none">
              <GridOverlay :video-dimensions="videoDimensions" />
            </div>
            
            <!-- Safe Area Overlay -->
            <div v-if="showSafeArea" class="absolute inset-0 pointer-events-none">
              <SafeAreaOverlay :video-dimensions="videoDimensions" />
            </div>
          </div>
        </div>
        
        <!-- Video Controls -->
        <div class="video-controls p-3 bg-gray-800 border-t border-gray-600">
          <div class="flex items-center gap-3">
            <!-- Play/Pause -->
            <a-button @click="togglePlayPause" size="small">
              <template #icon>
                <svg v-if="!isPlaying" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="5,3 19,12 5,21"/>
                </svg>
                <svg v-else width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="6" y="4" width="4" height="16"/>
                  <rect x="14" y="4" width="4" height="16"/>
                </svg>
              </template>
            </a-button>
            
            <!-- Time Display -->
            <span class="text-xs text-gray-400 font-mono">
              {{ formatTime(currentTime) }} / {{ formatTime(videoInfo.duration) }}
            </span>
            
            <!-- Progress Bar -->
            <div class="flex-1">
              <a-slider
                :value="currentTime"
                :max="videoInfo.duration"
                :step="0.1"
                @change="seekTo"
                :tip-formatter="formatTime"
              />
            </div>
            
            <!-- Volume -->
            <div class="flex items-center gap-2">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-400">
                <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"/>
                <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
              </svg>
              <a-slider
                v-model:value="volume"
                :max="100"
                style="width: 60px"
                size="small"
                @change="updateVolume"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Layers Panel (when in layers mode) -->
      <div v-if="viewMode === 'layers'" class="w-80 border-l border-gray-600">
        <VideoLayersPanel />
      </div>
    </div>
    
    <!-- Effects Toolbar -->
    <div class="effects-toolbar p-2 bg-gray-800 border-t border-gray-600">
      <div class="flex items-center gap-2 flex-wrap">
        <!-- Layer Toggles -->
        <a-button-group size="small">
          <a-button 
            :type="subtitleLayer?.enabled ? 'primary' : 'default'"
            @click="toggleLayer('subtitles')"
          >
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
            </template>
            Subtitle
          </a-button>
          
          <a-button 
            :type="showGrid ? 'primary' : 'default'"
            @click="showGrid = !showGrid"
          >
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <line x1="9" y1="9" x2="9" y2="15"/>
                <line x1="15" y1="9" x2="15" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="9"/>
                <line x1="9" y1="15" x2="15" y2="15"/>
              </svg>
            </template>
            Grid
          </a-button>
          
          <a-button 
            :type="showSafeArea ? 'primary' : 'default'"
            @click="showSafeArea = !showSafeArea"
          >
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
              </svg>
            </template>
            Safe
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <!-- Quick Effects -->
        <a-button size="small" @click="addTextOverlay">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="4,7 4,4 20,4 20,7"/>
              <line x1="9" y1="20" x2="15" y2="20"/>
              <line x1="12" y1="4" x2="12" y2="20"/>
            </svg>
          </template>
          Add Text
        </a-button>
        
        <a-button size="small" @click="addImageOverlay">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
          </template>
          Add Image
        </a-button>
        
        <a-button size="small" @click="addBlurEffect">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </template>
          Blur
        </a-button>
        
        <a-divider type="vertical" />
        
        <!-- Render Button -->
        <a-button type="primary" size="small" @click="renderVideo">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polygon points="10,8 16,12 10,16 10,8"/>
            </svg>
          </template>
          Render Video
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { state } from '@/lib/state'

// Components
import VideoLayersPanel from './VideoLayersPanel.vue'
import SubtitleOverlay from './overlays/SubtitleOverlay.vue'
import TextOverlay from './overlays/TextOverlay.vue'
import ImageOverlay from './overlays/ImageOverlay.vue'
import EffectOverlay from './overlays/EffectOverlay.vue'
import GridOverlay from './overlays/GridOverlay.vue'
import SafeAreaOverlay from './overlays/SafeAreaOverlay.vue'

// Stores
const layersStore = useVideoLayersStore()
const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Refs
const videoElement = ref(null)

// Local state
const viewMode = ref('preview') // 'preview' | 'layers'
const previewQuality = ref('medium')
const previewZoom = ref(100)
const showGrid = ref(false)
const showSafeArea = ref(false)
const volume = ref(100)
const currentTime = ref(0)
const isPlaying = ref(false)

// Video info
const videoInfo = computed(() => layersStore.videoInfo)
const videoSrc = computed(() => state.videoSrc || '')
const videoDimensions = computed(() => ({
  width: videoInfo.value.width,
  height: videoInfo.value.height
}))

// Layers
const subtitleLayer = computed(() => layersStore.subtitleLayer)
const enabledTextLayers = computed(() => 
  layersStore.enabledLayers.filter(layer => layer.type === 'text')
)
const enabledImageLayers = computed(() => 
  layersStore.enabledLayers.filter(layer => layer.type === 'image')
)
const activeEffects = computed(() => layersStore.activeEffects)

// Viewport styling
const viewportStyle = computed(() => {
  const zoom = previewZoom.value / 100
  const maxWidth = 800 * zoom
  const maxHeight = 450 * zoom
  
  return {
    maxWidth: maxWidth + 'px',
    maxHeight: maxHeight + 'px',
    transform: `scale(${zoom})`,
    transformOrigin: 'center center'
  }
})

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const onVideoLoaded = () => {
  if (videoElement.value) {
    const video = videoElement.value
    layersStore.setVideoInfo({
      width: video.videoWidth,
      height: video.videoHeight,
      duration: video.duration,
      src: video.src
    })
  }
}

const onTimeUpdate = () => {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime
    layersStore.setCurrentTime(currentTime.value)
    timelineStore.setCurrentTime(currentTime.value)
  }
}

const onPlay = () => {
  isPlaying.value = true
  layersStore.setPlaying(true)
}

const onPause = () => {
  isPlaying.value = false
  layersStore.setPlaying(false)
}

const togglePlayPause = () => {
  if (videoElement.value) {
    if (isPlaying.value) {
      videoElement.value.pause()
    } else {
      videoElement.value.play()
    }
  }
}

const seekTo = (time) => {
  if (videoElement.value) {
    videoElement.value.currentTime = time
    currentTime.value = time
  }
}

const updateVolume = (vol) => {
  if (videoElement.value) {
    videoElement.value.volume = vol / 100
  }
}

const handleVideoClick = (event) => {
  // Handle click for positioning elements
  const rect = event.currentTarget.getBoundingClientRect()
  const x = ((event.clientX - rect.left) / rect.width) * 100
  const y = ((event.clientY - rect.top) / rect.height) * 100
  
  console.log('Video clicked at:', { x, y })
}

const toggleLayer = (layerId) => {
  layersStore.toggleLayer(layerId)
}

const addTextOverlay = () => {
  layersStore.addLayer({
    type: 'text',
    name: 'Text Overlay',
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      position: { x: 50, y: 50 }
    }
  })
}

const addImageOverlay = () => {
  layersStore.addLayer({
    type: 'image',
    name: 'Image Overlay',
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100
    }
  })
}

const addBlurEffect = () => {
  layersStore.addEffect('blur', {
    x: 10,
    y: 10,
    width: 100,
    height: 50,
    intensity: 10
  })
}

const renderVideo = () => {
  // TODO: Implement video rendering
  console.log('Rendering video with layers:', layersStore.enabledLayers)
}

// Sync with timeline
watch(() => timelineStore.currentTime, (newTime) => {
  if (Math.abs(newTime - currentTime.value) > 0.1) {
    seekTo(newTime)
  }
})

// Lifecycle
onMounted(() => {
  // Set video player reference for timeline
  state.videoPlayer = { value: { $refs: { video: videoElement.value } } }
})

onUnmounted(() => {
  state.videoPlayer = null
})
</script>

<style scoped>
.video-preview-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.video-viewport {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid #374151;
}

.video-controls :deep(.ant-slider) {
  margin: 0;
}

.effects-toolbar {
  min-height: 48px;
}
</style>
