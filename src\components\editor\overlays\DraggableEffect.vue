<template>
  <div
    class="draggable-effect absolute border-2 border-dashed cursor-move"
    :class="effectClasses"
    :style="effectStyle"
    @mousedown="startDrag"
    @contextmenu.prevent="showContextMenu"
  >
    <!-- Effect Content -->
    <div class="effect-content absolute inset-0" :class="contentClasses">
      <!-- Effect Label -->
      <div class="effect-label absolute top-1 left-1 text-xs text-white px-1 rounded" :class="labelClasses">
        {{ effect.type.toUpperCase() }}
      </div>
      
      <!-- Effect Icon -->
      <div class="effect-icon absolute inset-0 flex items-center justify-center">
        <svg v-if="effect.type === 'blur'" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>
        <svg v-else-if="effect.type === 'delogo'" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        </svg>
      </div>
      
      <!-- Resize Handles -->
      <div v-if="isSelected" class="resize-handles">
        <div
          v-for="handle in resizeHandles"
          :key="handle.position"
          class="resize-handle absolute w-2 h-2 bg-white border border-gray-800"
          :class="handle.classes"
          :style="handle.style"
          @mousedown.stop="startResize($event, handle.position)"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  effect: {
    type: Object,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update', 'select', 'context-menu'])

// Drag state
const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const resizeDirection = ref('')

// Effect styling
const effectClasses = computed(() => ({
  'border-blue-500': props.effect.type === 'blur',
  'border-red-500': props.effect.type === 'delogo',
  'border-green-500': props.effect.type === 'color',
  'border-yellow-500': props.effect.type === 'crop',
  'border-opacity-100': props.isSelected,
  'border-opacity-60': !props.isSelected
}))

const contentClasses = computed(() => ({
  'bg-blue-500': props.effect.type === 'blur',
  'bg-red-500': props.effect.type === 'delogo',
  'bg-green-500': props.effect.type === 'color',
  'bg-yellow-500': props.effect.type === 'crop',
  'bg-opacity-20': true
}))

const labelClasses = computed(() => ({
  'bg-blue-500': props.effect.type === 'blur',
  'bg-red-500': props.effect.type === 'delogo',
  'bg-green-500': props.effect.type === 'color',
  'bg-yellow-500': props.effect.type === 'crop'
}))

const effectStyle = computed(() => {
  const { width, height } = props.videoDimensions
  
  // Convert effect coordinates to pixels
  let x, y, w, h
  
  if (props.effect.x <= 1 && props.effect.y <= 1) {
    // Normalized coordinates (0-1)
    x = props.effect.x * width
    y = props.effect.y * height
    w = props.effect.width * width
    h = props.effect.height * height
  } else {
    // Pixel coordinates
    x = props.effect.x
    y = props.effect.y
    w = props.effect.width
    h = props.effect.height
  }
  
  return {
    left: x + 'px',
    top: y + 'px',
    width: w + 'px',
    height: h + 'px',
    zIndex: props.isSelected ? 20 : 10
  }
})

// Resize handles
const resizeHandles = computed(() => [
  { position: 'nw', classes: 'cursor-nw-resize', style: { top: '-4px', left: '-4px' } },
  { position: 'ne', classes: 'cursor-ne-resize', style: { top: '-4px', right: '-4px' } },
  { position: 'sw', classes: 'cursor-sw-resize', style: { bottom: '-4px', left: '-4px' } },
  { position: 'se', classes: 'cursor-se-resize', style: { bottom: '-4px', right: '-4px' } },
  { position: 'n', classes: 'cursor-n-resize', style: { top: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 's', classes: 'cursor-s-resize', style: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 'w', classes: 'cursor-w-resize', style: { top: '50%', left: '-4px', transform: 'translateY(-50%)' } },
  { position: 'e', classes: 'cursor-e-resize', style: { top: '50%', right: '-4px', transform: 'translateY(-50%)' } }
])

// Methods
const startDrag = (event) => {
  if (isResizing.value) return
  
  isDragging.value = true
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
  
  emit('select', props.effect.id)
  
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', endDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y
  
  const { width, height } = props.videoDimensions
  
  // Convert pixel delta to normalized coordinates
  const deltaXNorm = deltaX / width
  const deltaYNorm = deltaY / height
  
  // Update effect position
  const newX = Math.max(0, Math.min(1 - props.effect.width, props.effect.x + deltaXNorm))
  const newY = Math.max(0, Math.min(1 - props.effect.height, props.effect.y + deltaYNorm))
  
  emit('update', {
    ...props.effect,
    x: newX,
    y: newY
  })
  
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
}

const endDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
}

const startResize = (event, direction) => {
  isResizing.value = true
  resizeDirection.value = direction
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', endResize)
}

const handleResize = (event) => {
  if (!isResizing.value) return
  
  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y
  
  const { width, height } = props.videoDimensions
  
  // Convert pixel delta to normalized coordinates
  const deltaXNorm = deltaX / width
  const deltaYNorm = deltaY / height
  
  let newX = props.effect.x
  let newY = props.effect.y
  let newWidth = props.effect.width
  let newHeight = props.effect.height
  
  // Handle different resize directions
  const direction = resizeDirection.value
  
  if (direction.includes('n')) {
    newY += deltaYNorm
    newHeight -= deltaYNorm
  }
  if (direction.includes('s')) {
    newHeight += deltaYNorm
  }
  if (direction.includes('w')) {
    newX += deltaXNorm
    newWidth -= deltaXNorm
  }
  if (direction.includes('e')) {
    newWidth += deltaXNorm
  }
  
  // Constrain to bounds
  newX = Math.max(0, newX)
  newY = Math.max(0, newY)
  newWidth = Math.max(0.05, Math.min(1 - newX, newWidth))
  newHeight = Math.max(0.05, Math.min(1 - newY, newHeight))
  
  emit('update', {
    ...props.effect,
    x: newX,
    y: newY,
    width: newWidth,
    height: newHeight
  })
  
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
}

const endResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', endResize)
}

const showContextMenu = (event) => {
  emit('context-menu', event, props.effect)
}

// Cleanup
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', endResize)
})
</script>

<style scoped>
.draggable-effect {
  transition: border-color 0.2s ease;
}

.draggable-effect:hover {
  border-width: 3px;
}

.resize-handle {
  transition: all 0.2s ease;
}

.resize-handle:hover {
  background-color: #3b82f6;
  transform: scale(1.2);
}
</style>
