<template>
  <div class="effects-panel p-3 space-y-4">
    <!-- Effects Categories -->
    <div class="grid grid-cols-2 gap-3">
      <!-- Video Effects -->
      <div class="effects-category">
        <h4 class="text-xs font-medium text-gray-300 mb-2">VIDEO EFFECTS</h4>
        <div class="space-y-2">
          <a-button size="small" block @click="addBlurEffect">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
              </svg>
            </template>
            Blur Area
          </a-button>
          
          <a-button size="small" block @click="addDelogoEffect">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
            </template>
            Remove Logo
          </a-button>
          
          <a-button size="small" block @click="addColorEffect">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 2a7 7 0 1 0 10 10"/>
              </svg>
            </template>
            Color Correction
          </a-button>
          
          <a-button size="small" block @click="addCropEffect">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6 2v14a2 2 0 0 0 2 2h14"/>
                <path d="M18 6H8a2 2 0 0 0-2 2v10"/>
              </svg>
            </template>
            Crop Video
          </a-button>
        </div>
      </div>
      
      <!-- Text Effects -->
      <div class="effects-category">
        <h4 class="text-xs font-medium text-gray-300 mb-2">TEXT EFFECTS</h4>
        <div class="space-y-2">
          <a-button size="small" block @click="addTextEffect('fade')">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2v20"/>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
            </template>
            Fade In/Out
          </a-button>
          
          <a-button size="small" block @click="addTextEffect('typewriter')">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
            </template>
            Typewriter
          </a-button>
          
          <a-button size="small" block @click="addTextEffect('slide')">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 12h18m-9-9l9 9-9 9"/>
              </svg>
            </template>
            Slide In
          </a-button>
          
          <a-button size="small" block @click="addTextEffect('bounce')">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2v20"/>
                <path d="M8 18l4-4 4 4"/>
                <path d="M8 6l4 4 4-4"/>
              </svg>
            </template>
            Bounce
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- Active Effects -->
    <div v-if="activeEffects.length > 0" class="active-effects">
      <h4 class="text-xs font-medium text-gray-300 mb-2">ACTIVE EFFECTS</h4>
      <div class="space-y-2 max-h-40 overflow-auto">
        <div
          v-for="effect in activeEffects"
          :key="effect.id"
          class="effect-item bg-gray-700 rounded p-2 flex items-center justify-between"
        >
          <div class="flex items-center gap-2">
            <!-- Effect Icon -->
            <div class="effect-icon">
              <svg v-if="effect.type === 'blur'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
              </svg>
              <svg v-else-if="effect.type === 'delogo'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
              <svg v-else-if="effect.type === 'color'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 2a7 7 0 1 0 10 10"/>
              </svg>
              <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              </svg>
            </div>
            
            <!-- Effect Info -->
            <div class="text-xs">
              <div class="font-medium capitalize">{{ effect.type }}</div>
              <div class="text-gray-400">
                {{ formatTime(effect.timeStart) }} - {{ formatTime(effect.timeEnd) }}
              </div>
            </div>
          </div>
          
          <!-- Effect Controls -->
          <div class="flex items-center gap-1">
            <a-button size="small" @click="editEffect(effect)" title="Edit">
              <template #icon>
                <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
              </template>
            </a-button>
            
            <a-button size="small" @click="removeEffect(effect)" title="Remove">
              <template #icon>
                <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"/>
                  <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                </svg>
              </template>
            </a-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Subtitle Settings -->
    <div class="subtitle-settings">
      <h4 class="text-xs font-medium text-gray-300 mb-2">SUBTITLE SETTINGS</h4>
      <div class="space-y-2">
        <!-- Font Size -->
        <div>
          <label class="text-xs text-gray-400 block mb-1">Font Size</label>
          <a-slider
            :value="subtitleSettings.fontSize"
            @change="updateSubtitleSetting('fontSize', $event)"
            :min="12"
            :max="100"
            size="small"
          />
        </div>
        
        <!-- Colors -->
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="text-xs text-gray-400 block mb-1">Text Color</label>
            <input
              type="color"
              :value="subtitleSettings.color"
              @input="updateSubtitleSetting('color', $event.target.value)"
              class="w-full h-6 rounded border border-gray-600"
            />
          </div>
          <div>
            <label class="text-xs text-gray-400 block mb-1">Background</label>
            <input
              type="color"
              :value="subtitleSettings.backgroundColor"
              @input="updateSubtitleSetting('backgroundColor', $event.target.value)"
              class="w-full h-6 rounded border border-gray-600"
            />
          </div>
        </div>
        
        <!-- Position -->
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="text-xs text-gray-400 block mb-1">X Position</label>
            <a-slider
              :value="subtitleSettings.position.x"
              @change="updateSubtitlePosition('x', $event)"
              :min="0"
              :max="100"
              size="small"
            />
          </div>
          <div>
            <label class="text-xs text-gray-400 block mb-1">Y Position</label>
            <a-slider
              :value="subtitleSettings.position.y"
              @change="updateSubtitlePosition('y', $event)"
              :min="0"
              :max="100"
              size="small"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Render Settings -->
    <div class="render-settings">
      <h4 class="text-xs font-medium text-gray-300 mb-2">RENDER SETTINGS</h4>
      <div class="space-y-2">
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="text-xs text-gray-400 block mb-1">Resolution</label>
            <a-select
              :value="renderSettings.resolution"
              @change="updateRenderSetting('resolution', $event)"
              size="small"
              class="w-full"
            >
              <a-select-option value="1920x1080">1080p</a-select-option>
              <a-select-option value="1280x720">720p</a-select-option>
              <a-select-option value="3840x2160">4K</a-select-option>
            </a-select>
          </div>
          <div>
            <label class="text-xs text-gray-400 block mb-1">FPS</label>
            <a-select
              :value="renderSettings.fps"
              @change="updateRenderSetting('fps', $event)"
              size="small"
              class="w-full"
            >
              <a-select-option :value="24">24</a-select-option>
              <a-select-option :value="30">30</a-select-option>
              <a-select-option :value="60">60</a-select-option>
            </a-select>
          </div>
        </div>
        
        <div>
          <label class="text-xs text-gray-400 block mb-1">Quality</label>
          <a-slider
            :value="renderSettings.bitrate"
            @change="updateRenderSetting('bitrate', $event)"
            :min="1000"
            :max="20000"
            :step="500"
            size="small"
          />
          <div class="text-xs text-gray-400 mt-1">{{ renderSettings.bitrate }}kbps</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'

const layersStore = useVideoLayersStore()

// Computed
const activeEffects = computed(() => {
  const effects = layersStore.activeEffects
  return [
    ...effects.delogo.map(e => ({ ...e, type: 'delogo' })),
    ...effects.blur.map(e => ({ ...e, type: 'blur' })),
    ...effects.text.map(e => ({ ...e, type: 'text' })),
    ...effects.image.map(e => ({ ...e, type: 'image' }))
  ]
})

const subtitleSettings = computed(() => layersStore.subtitleLayer?.properties || {})
const renderSettings = computed(() => layersStore.export)

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const addBlurEffect = () => {
  layersStore.addEffect('blur', {
    x: 0.1,
    y: 0.1,
    width: 0.2,
    height: 0.2,
    intensity: 10
  })
}

const addDelogoEffect = () => {
  layersStore.addEffect('delogo', {
    x: 0.8,
    y: 0.05,
    width: 0.15,
    height: 0.1
  })
}

const addColorEffect = () => {
  layersStore.addEffect('color', {
    x: 0,
    y: 0,
    width: 1,
    height: 1,
    brightness: 0,
    contrast: 1,
    saturation: 1
  })
}

const addCropEffect = () => {
  layersStore.addEffect('crop', {
    x: 0.1,
    y: 0.1,
    width: 0.8,
    height: 0.8
  })
}

const addTextEffect = (type) => {
  // Add text animation effect
  const textLayer = layersStore.getLayerById('text-overlay')
  if (textLayer) {
    layersStore.updateLayerProperty('text-overlay', 'properties.animation.type', type)
  }
}

const editEffect = (effect) => {
  console.log('Edit effect:', effect)
  // TODO: Open effect editor modal
}

const removeEffect = (effect) => {
  layersStore.removeEffect(effect.type, effect.id)
}

const updateSubtitleSetting = (property, value) => {
  layersStore.updateLayerProperty('subtitles', `properties.${property}`, value)
}

const updateSubtitlePosition = (axis, value) => {
  layersStore.updateLayerProperty('subtitles', `properties.position.${axis}`, value)
}

const updateRenderSetting = (property, value) => {
  layersStore.updateExportSettings({ [property]: value })
}
</script>

<style scoped>
.effects-panel {
  max-height: 400px;
  overflow-y: auto;
}

.effects-panel :deep(.ant-slider) {
  margin: 0;
}

.effects-panel :deep(.ant-select) {
  width: 100%;
}

.effect-item:hover {
  background-color: #4b5563;
}
</style>
