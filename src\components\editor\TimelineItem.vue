<template>
  <div
    class="timeline-item absolute cursor-pointer select-none"
    :class="{
      'selected': isSelected,
      'dragging': isDragging,
      'hover': isHovered
    }"
    :style="itemStyle"
    @mousedown="handleMouseDown"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @contextmenu="handleContextMenu"
  >
    <!-- Left resize handle -->
    <div
      class="resize-handle resize-left absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-blue-500"
      @mousedown.stop="handleResizeStart('left', $event)"
    ></div>
    
    <!-- Item content -->
    <div class="item-content h-full px-2 py-1 overflow-hidden">
      <!-- Item header -->
      <div class="flex items-center justify-between text-xs mb-1">
        <span class="font-medium text-white">{{ item.id }}</span>
        <span class="text-gray-300">{{ formatDuration(duration) }}</span>
      </div>
      
      <!-- Item text -->
      <div class="text-xs text-gray-200 leading-tight line-clamp-2">
        {{ displayText }}
      </div>
      
      <!-- Audio status indicators -->
      <div v-if="hasAudio" class="flex items-center gap-1 mt-1">
        <div
          v-if="item.isGenerated1"
          class="w-2 h-2 bg-green-500 rounded-full"
          title="Voice 1 generated"
        ></div>
        <div
          v-if="item.isGenerated2"
          class="w-2 h-2 bg-blue-500 rounded-full"
          title="Voice 2 generated"
        ></div>
        <div
          v-if="item.isGenerated3"
          class="w-2 h-2 bg-purple-500 rounded-full"
          title="Voice 3 generated"
        ></div>
      </div>
    </div>
    
    <!-- Right resize handle -->
    <div
      class="resize-handle resize-right absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-blue-500"
      @mousedown.stop="handleResizeStart('right', $event)"
    ></div>
    
    <!-- Selection indicator -->
    <div
      v-if="isSelected"
      class="absolute inset-0 border-2 border-blue-500 pointer-events-none"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'select',
  'drag-start',
  'drag',
  'drag-end',
  'resize-start',
  'resize',
  'resize-end',
  'context-menu'
])

const timelineStore = useTimelineStore()

// Local state
const isHovered = ref(false)
const isDragging = ref(false)
const dragStartX = ref(0)

// Computed
const isSelected = computed(() => {
  return timelineStore.isItemSelected(props.item.id)
})

const duration = computed(() => {
  return props.item.endTime - props.item.startTime
})

const itemStyle = computed(() => {
  const left = timelineStore.timeToPixel(props.item.startTime)
  const width = timelineStore.timeToPixel(duration.value)
  const minWidth = 20 // Minimum width in pixels
  
  return {
    left: left + 'px',
    width: Math.max(width, minWidth) + 'px',
    top: '5px',
    height: (timelineStore.trackHeight - 10) + 'px',
    backgroundColor: getItemColor(),
    border: `1px solid ${getItemBorderColor()}`,
    borderRadius: '4px',
    zIndex: isSelected.value ? 10 : 1
  }
})

const displayText = computed(() => {
  // Show translated text if available, otherwise original text
  return props.item.translatedText || props.item.text || `Subtitle ${props.item.id}`
})

const hasAudio = computed(() => {
  return props.item.isGenerated1 || props.item.isGenerated2 || props.item.isGenerated3
})

// Methods
const getItemColor = () => {
  if (isSelected.value) {
    return '#3b82f6' // Blue for selected
  } else if (props.item.status === 'translated') {
    return '#10b981' // Green for translated
  } else if (props.item.status === 'translating') {
    return '#f59e0b' // Yellow for translating
  } else if (props.item.status === 'error') {
    return '#ef4444' // Red for error
  } else {
    return '#6b7280' // Gray for pending
  }
}

const getItemBorderColor = () => {
  if (isSelected.value) {
    return '#1d4ed8'
  } else if (isHovered.value) {
    return '#60a5fa'
  } else {
    return '#374151'
  }
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0.0s'
  return seconds.toFixed(1) + 's'
}

const handleMouseDown = (event) => {
  if (event.target.classList.contains('resize-handle')) return
  
  emit('select', props.item, event)
  
  if (event.button === 0) { // Left mouse button
    isDragging.value = true
    dragStartX.value = event.clientX
    
    emit('drag-start', props.item, event)
    
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'grabbing'
    document.body.style.userSelect = 'none'
  }
}

const handleMouseMove = (event) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStartX.value
  emit('drag', deltaX)
}

const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    emit('drag-end')
    
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}

const handleResizeStart = (edge, event) => {
  event.preventDefault()
  event.stopPropagation()
  
  emit('resize-start', props.item, edge, event)
  
  const handleResizeMove = (moveEvent) => {
    const deltaX = moveEvent.clientX - event.clientX
    emit('resize', deltaX)
  }
  
  const handleResizeEnd = () => {
    emit('resize-end')
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
  
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none'
}

const handleContextMenu = (event) => {
  emit('context-menu', props.item, event)
}
</script>

<style scoped>
.timeline-item {
  transition: all 0.1s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.timeline-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-item.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-item.dragging {
  opacity: 0.8;
  transform: scale(1.02);
}

.resize-handle {
  transition: opacity 0.2s ease;
}

.timeline-item:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle:hover {
  opacity: 1 !important;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-content {
  pointer-events: none;
}
</style>
