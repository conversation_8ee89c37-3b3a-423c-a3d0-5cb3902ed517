import { defineStore } from 'pinia';
import { useTTSStore } from './ttsStore';
import { useSubtitleStore } from './subtitle-store';

export const useVideoLayersStore = defineStore('videoLayers', {
  state: () => ({
    // Video properties
    videoInfo: {
      width: 1920,
      height: 1080,
      duration: 0,
      fps: 30
    },
    
    // Layers system
    layers: [
      // Layer 0: Video background (always present)
      {
        id: 'video-bg',
        type: 'video',
        name: 'Video Background',
        enabled: true,
        locked: false,
        opacity: 100,
        blendMode: 'normal',
        zIndex: 0,
        timeRange: { start: 0, end: 0 },
        properties: {
          src: '',
          volume: 100,
          speed: 1.0,
          filters: {
            brightness: 0,
            contrast: 1,
            saturation: 1,
            blur: 0,
            sharpen: 0
          }
        }
      },
      
      // Layer 1: Subtitle layer
      {
        id: 'subtitles',
        type: 'subtitle',
        name: 'Subtitles',
        enabled: true,
        locked: false,
        opacity: 100,
        blendMode: 'normal',
        zIndex: 1,
        timeRange: { start: 0, end: 0 },
        properties: {
          fontSize: 48,
          fontFamily: 'Arial',
          color: '#ffffff',
          backgroundColor: '#000000',
          borderColor: '#000000',
          borderWidth: 2,
          bold: true,
          italic: false,
          underline: false,
          alignment: 'center', // left, center, right
          position: { x: 50, y: 85 }, // percentage
          shadow: {
            enabled: true,
            color: '#000000',
            blur: 3,
            offsetX: 2,
            offsetY: 2
          },
          animation: {
            type: 'fade', // fade, slide, typewriter, bounce
            duration: 0.3,
            easing: 'ease-in-out'
          }
        }
      }
    ],
    
    // Effects and overlays
    effects: {
      // Delogo areas
      delogoAreas: [],
      
      // Blur areas  
      blurAreas: [],
      
      // Text overlays
      textOverlays: [],
      
      // Image overlays (logos, watermarks)
      imageOverlays: [],
      
      // Audio effects
      audioEffects: {
        volume: 100,
        fadeIn: 0,
        fadeOut: 0,
        noise: 0,
        echo: false
      }
    },
    
    // Preview settings
    preview: {
      currentTime: 0,
      isPlaying: false,
      showGrid: false,
      showSafeArea: false,
      quality: 'medium', // low, medium, high
      zoom: 100
    },
    
    // Export settings
    export: {
      resolution: '1920x1080',
      fps: 30,
      bitrate: 5000,
      codec: 'h264',
      format: 'mp4'
    }
  }),
  
  getters: {
    // Get layers sorted by zIndex
    sortedLayers: (state) => {
      return [...state.layers].sort((a, b) => a.zIndex - b.zIndex);
    },
    
    // Get enabled layers only
    enabledLayers: (state) => {
      return state.layers.filter(layer => layer.enabled);
    },
    
    // Get layer by ID
    getLayerById: (state) => (id) => {
      return state.layers.find(layer => layer.id === id);
    },
    
    // Get subtitle layer
    subtitleLayer: (state) => {
      return state.layers.find(layer => layer.type === 'subtitle');
    },
    
    // Get video background layer
    videoLayer: (state) => {
      return state.layers.find(layer => layer.type === 'video');
    },
    
    // Get current subtitle settings based on voice
    currentSubtitleSettings: (state) => {
      const ttsStore = useTTSStore();
      const subtitleStore = useSubtitleStore();
      
      if (!ttsStore.currentSrtList?.items?.length) {
        return state.subtitleLayer?.properties || {};
      }
      
      // Get current playing subtitle
      const currentSubtitle = ttsStore.currentSrtList.items.find(item => 
        item.id === state.preview.currentSubtitleId
      );
      
      if (!currentSubtitle || !currentSubtitle.isVoice) {
        return state.subtitleLayer?.properties || {};
      }
      
      // Get voice-specific settings
      const voiceKey = `isVoice${currentSubtitle.isVoice}`;
      const voiceConfig = subtitleStore.renderVoiceOptions[voiceKey];
      
      if (!voiceConfig) {
        return state.subtitleLayer?.properties || {};
      }
      
      return {
        ...state.subtitleLayer?.properties,
        fontSize: voiceConfig.subtitleFontSize,
        color: voiceConfig.subtitleTextColor,
        backgroundColor: voiceConfig.subtitleBackgroundColor,
        borderColor: voiceConfig.subtitleBorderColor,
        bold: voiceConfig.subtitleBold,
        shadow: {
          ...state.subtitleLayer?.properties?.shadow,
          blur: voiceConfig.shadowSize
        },
        position: {
          x: voiceConfig.assOptions?.posX || 50,
          y: voiceConfig.assOptions?.posY || 85
        },
        alignment: voiceConfig.assOptions?.align === 1 ? 'left' : 
                  voiceConfig.assOptions?.align === 3 ? 'right' : 'center'
      };
    },
    
    // Get all effects for current time
    activeEffects: (state) => {
      const currentTime = state.preview.currentTime;
      
      return {
        delogo: state.effects.delogoAreas.filter(area => 
          currentTime >= area.timeStart && currentTime <= area.timeEnd
        ),
        blur: state.effects.blurAreas.filter(area => 
          currentTime >= area.timeStart && currentTime <= area.timeEnd
        ),
        text: state.effects.textOverlays.filter(overlay => 
          currentTime >= overlay.timeStart && currentTime <= overlay.timeEnd
        ),
        image: state.effects.imageOverlays.filter(overlay => 
          currentTime >= overlay.timeStart && currentTime <= overlay.timeEnd
        )
      };
    }
  },
  
  actions: {
    // Initialize video info
    setVideoInfo(info) {
      this.videoInfo = { ...this.videoInfo, ...info };
      
      // Update video layer duration
      const videoLayer = this.getLayerById('video-bg');
      if (videoLayer) {
        videoLayer.timeRange.end = info.duration;
        videoLayer.properties.src = info.src;
      }
      
      // Update subtitle layer duration
      const subtitleLayer = this.getLayerById('subtitles');
      if (subtitleLayer) {
        subtitleLayer.timeRange.end = info.duration;
      }
    },
    
    // Layer management
    addLayer(layer) {
      const newLayer = {
        id: `layer-${Date.now()}`,
        enabled: true,
        locked: false,
        opacity: 100,
        blendMode: 'normal',
        zIndex: this.layers.length,
        timeRange: { start: 0, end: this.videoInfo.duration },
        ...layer
      };
      
      this.layers.push(newLayer);
      return newLayer.id;
    },
    
    removeLayer(id) {
      const index = this.layers.findIndex(layer => layer.id === id);
      if (index > -1 && !this.layers[index].locked) {
        this.layers.splice(index, 1);
      }
    },
    
    updateLayer(id, updates) {
      const layer = this.getLayerById(id);
      if (layer && !layer.locked) {
        Object.assign(layer, updates);
      }
    },
    
    updateLayerProperty(id, property, value) {
      const layer = this.getLayerById(id);
      if (layer && !layer.locked) {
        if (property.includes('.')) {
          const keys = property.split('.');
          let obj = layer.properties;
          for (let i = 0; i < keys.length - 1; i++) {
            obj = obj[keys[i]];
          }
          obj[keys[keys.length - 1]] = value;
        } else {
          layer.properties[property] = value;
        }
      }
    },
    
    toggleLayer(id) {
      const layer = this.getLayerById(id);
      if (layer) {
        layer.enabled = !layer.enabled;
      }
    },
    
    reorderLayers(fromIndex, toIndex) {
      const layer = this.layers.splice(fromIndex, 1)[0];
      this.layers.splice(toIndex, 0, layer);
      
      // Update zIndex
      this.layers.forEach((layer, index) => {
        layer.zIndex = index;
      });
    },
    
    // Effects management
    addEffect(type, effect) {
      const effectWithId = {
        id: `${type}-${Date.now()}`,
        timeStart: this.preview.currentTime,
        timeEnd: this.preview.currentTime + 5,
        ...effect
      };
      
      switch (type) {
        case 'delogo':
          this.effects.delogoAreas.push(effectWithId);
          break;
        case 'blur':
          this.effects.blurAreas.push(effectWithId);
          break;
        case 'text':
          this.effects.textOverlays.push(effectWithId);
          break;
        case 'image':
          this.effects.imageOverlays.push(effectWithId);
          break;
      }
      
      return effectWithId.id;
    },
    
    removeEffect(type, id) {
      switch (type) {
        case 'delogo':
          this.effects.delogoAreas = this.effects.delogoAreas.filter(e => e.id !== id);
          break;
        case 'blur':
          this.effects.blurAreas = this.effects.blurAreas.filter(e => e.id !== id);
          break;
        case 'text':
          this.effects.textOverlays = this.effects.textOverlays.filter(e => e.id !== id);
          break;
        case 'image':
          this.effects.imageOverlays = this.effects.imageOverlays.filter(e => e.id !== id);
          break;
      }
    },
    
    updateEffect(type, id, updates) {
      let effects;
      switch (type) {
        case 'delogo':
          effects = this.effects.delogoAreas;
          break;
        case 'blur':
          effects = this.effects.blurAreas;
          break;
        case 'text':
          effects = this.effects.textOverlays;
          break;
        case 'image':
          effects = this.effects.imageOverlays;
          break;
      }
      
      const effect = effects?.find(e => e.id === id);
      if (effect) {
        Object.assign(effect, updates);
      }
    },
    
    // Preview controls
    setCurrentTime(time) {
      this.preview.currentTime = Math.max(0, Math.min(time, this.videoInfo.duration));
    },
    
    setPlaying(playing) {
      this.preview.isPlaying = playing;
    },
    
    // Export configuration
    updateExportSettings(settings) {
      Object.assign(this.export, settings);
    }
  }
});
