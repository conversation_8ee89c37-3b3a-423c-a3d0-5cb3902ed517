<template>
  <div class="subtitle-overlay absolute inset-0 pointer-events-none">
    <div
      v-if="currentSubtitle && displayText"
      class="subtitle-text absolute transition-all duration-300"
      :style="subtitleStyle"
      @click.stop="handleSubtitleClick"
    >
      {{ displayText }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTTSStore } from '@/stores/ttsStore'
import { useSubtitleStore } from '@/stores/subtitle-store'
import { parseTimeToSeconds } from '@/lib/utils'

const props = defineProps({
  currentTime: {
    type: Number,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['subtitle-click'])

const ttsStore = useTTSStore()
const subtitleStore = useSubtitleStore()

// Get current subtitle based on time
const currentSubtitle = computed(() => {
  if (!ttsStore.currentSrtList?.items) return null

  return ttsStore.currentSrtList.items.find(item => {
    const startTime = item.startTime || parseTimeToSeconds(item.start)
    const endTime = item.endTime || parseTimeToSeconds(item.end)
    return props.currentTime >= startTime && props.currentTime <= endTime
  })
})

// Default subtitle settings
const defaultSettings = {
  fontSize: 48,
  fontFamily: 'Arial',
  color: '#ffffff',
  backgroundColor: 'transparent',
  borderColor: '#000000',
  borderWidth: 2,
  bold: true,
  italic: false,
  underline: false,
  alignment: 'center',
  position: { x: 50, y: 85 },
  shadow: {
    enabled: true,
    color: '#000000',
    blur: 3,
    offsetX: 2,
    offsetY: 2
  }
}

// Get voice-specific settings
const voiceSettings = computed(() => {
  if (!currentSubtitle.value || !currentSubtitle.value.isVoice) {
    return defaultSettings
  }

  const voiceKey = `isVoice${currentSubtitle.value.isVoice}`
  const voiceConfig = subtitleStore.renderVoiceOptions[voiceKey]

  if (!voiceConfig) {
    return defaultSettings
  }

  return {
    ...defaultSettings,
    fontSize: voiceConfig.subtitleFontSize || defaultSettings.fontSize,
    color: voiceConfig.subtitleTextColor || defaultSettings.color,
    backgroundColor: voiceConfig.subtitleBackgroundColor || defaultSettings.backgroundColor,
    borderColor: voiceConfig.subtitleBorderColor || defaultSettings.borderColor,
    bold: voiceConfig.subtitleBold !== undefined ? voiceConfig.subtitleBold : defaultSettings.bold,
    shadow: {
      ...defaultSettings.shadow,
      blur: voiceConfig.shadowSize || defaultSettings.shadow.blur
    },
    position: {
      x: voiceConfig.assOptions?.posX || defaultSettings.position.x,
      y: voiceConfig.assOptions?.posY || defaultSettings.position.y
    },
    alignment: voiceConfig.assOptions?.align === 1 ? 'left' :
              voiceConfig.assOptions?.align === 3 ? 'right' : 'center'
  }
})

// Display text (translated or original)
const displayText = computed(() => {
  if (!currentSubtitle.value) return ''
  return currentSubtitle.value.translatedText || currentSubtitle.value.text || ''
})

// Calculate subtitle styling
const subtitleStyle = computed(() => {
  if (!currentSubtitle.value || !displayText.value) return { display: 'none' }

  const settings = voiceSettings.value
  const { width, height } = props.videoDimensions

  // Calculate position
  const x = (settings.position.x / 100) * width
  const y = (settings.position.y / 100) * height

  // Calculate font size relative to video size
  const scaleFactor = Math.min(width / 1920, height / 1080)
  const fontSize = settings.fontSize * scaleFactor

  // Text shadow for border effect
  const borderColor = settings.borderColor || '#000000'
  const borderWidth = settings.borderWidth || 2
  const textShadow = settings.borderWidth > 0 ?
    `${-borderWidth}px ${-borderWidth}px 0 ${borderColor},
     ${borderWidth}px ${-borderWidth}px 0 ${borderColor},
     ${-borderWidth}px ${borderWidth}px 0 ${borderColor},
     ${borderWidth}px ${borderWidth}px 0 ${borderColor}` : 'none'

  // Shadow effect
  const shadowEffect = settings.shadow?.enabled ?
    `, ${settings.shadow.offsetX}px ${settings.shadow.offsetY}px ${settings.shadow.blur}px ${settings.shadow.color}` : ''

  return {
    left: x + 'px',
    top: y + 'px',
    fontSize: fontSize + 'px',
    fontFamily: settings.fontFamily || 'Arial',
    color: settings.color || '#ffffff',
    backgroundColor: settings.backgroundColor === 'transparent' ? 'transparent' : settings.backgroundColor,
    fontWeight: settings.bold ? 'bold' : 'normal',
    fontStyle: settings.italic ? 'italic' : 'normal',
    textDecoration: settings.underline ? 'underline' : 'none',
    textAlign: settings.alignment || 'center',
    textShadow: textShadow + shadowEffect,
    padding: '4px 8px',
    borderRadius: '4px',
    whiteSpace: 'nowrap',
    maxWidth: '90%',
    transform: 'translateX(-50%)',
    transformOrigin: 'center',
    pointerEvents: 'auto',
    cursor: 'pointer',
    zIndex: 10,
    // Animation
    opacity: 1,
    transition: 'all 0.3s ease'
  }
})

const handleSubtitleClick = () => {
  emit('subtitle-click', currentSubtitle.value)
}
</script>

<style scoped>
.subtitle-text:hover {
  transform: translateX(-50%) scale(1.05);
  filter: brightness(1.1);
}
</style>
