<template>
  <!-- Container chiếm full height với layout pattern chuẩn -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white min-h-0">
    <!-- Header - <PERSON><PERSON> định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-white">Panel Page Test</h2>
        <div class="flex space-x-2 text-sm text-gray-400">
          <span>Container: {{ containerWidth }}x{{ containerHeight }}</span>
          <span>Top: {{ topHeightPercent }}%</span>
          <span>Left: {{ leftWidthPercent }}%</span>
        </div>
      </div>
    </div>

    <!-- Content area - PanelPage -->
    <div class="flex-1 min-h-0">
      <PanelPage />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import PanelPage from './editor/PanelPage.vue'

// Debug info
const containerWidth = ref(0)
const containerHeight = ref(0)
const topHeightPercent = ref(0)
const leftWidthPercent = ref(0)

const updateDebugInfo = () => {
  const tabPaneElement = document.querySelector('.ant-tabs-tabpane-active')
  if (tabPaneElement) {
    containerWidth.value = tabPaneElement.clientWidth
    containerHeight.value = tabPaneElement.clientHeight
  }
}

onMounted(() => {
  updateDebugInfo()
  window.addEventListener('resize', updateDebugInfo)
  
  // Update debug info every second
  const interval = setInterval(() => {
    updateDebugInfo()
  }, 1000)
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateDebugInfo)
    clearInterval(interval)
  })
})
</script>

<style scoped>
/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
