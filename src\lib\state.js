import { reactive } from "vue";

export const state = reactive({
  contentStream: null,
  videoPlayer: null,
  currentPlayingSubtitleId: null,
  currentTime: 0,
  cropData: null,
  cropText: null,
  assOptionsForVideo:{},
  activeTab:'editor',
  tabs:[
  {
    key: '/',
    label: 'Editor',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      // video icon path
      h('path', { d: 'M5 4l14 0' }),
      h('path', { d: 'M5 8l14 0' }),
      h('path', { d: 'M5 12l14 0' }),
      h('path', { d: 'M5 16l14 0' }),



    ])
  },
  {
    key: '/dashboard',
    label: 'Tools',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      // tools ai icon path
      h('path', { d: 'M5 4l14 0' }),
      h('path', { d: 'M5 8l14 0' }),
      h('path', { d: 'M5 12l14 0' }),
      h('path', { d: 'M5 16l14 0' }),
    ])
  }
]
});

window.S = state;