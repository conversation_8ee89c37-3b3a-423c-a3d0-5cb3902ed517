<template>
  <div class="flex-column">
    <div class="absolute inset-0 flex-column overflow-auto">
      <div class="flex-column flex-1">

        <div class="flex-column flex-1">
          <!-- Top section (resizable) -->
          <div class="flex-shrink-0 bg-gray-800 shadow-lg border-b border-gray-700">
            <div class="flex items-center h-8 px-4">
              <div class="flex-column flex-1">
                <router-link to="/">Home</router-link>
              </div>
              <div class="flex-column flex-1">
                <router-link to="/dashboard">Dashboard</router-link>
              </div>
            </div>
          </div>
          <div class="flex border-b border-gray-600 flex-shrink-0" :style="{ height: topHeight + 'px' }">
            <!-- Left panel - Video -->
            <div class="border-r border-gray-600 flex items-center justify-center text-white flex-shrink-0"
              :style="{ width: leftWidth + 'px' }">
              <VideoPrevivewEditor />
            </div>

            <!-- Vertical resizer -->
            <div class="w-1 bg-gray-700 hover:bg-cyan-500 cursor-col-resize transition-colors duration-200"
              @mousedown="startVerticalResize"></div>

            <SubtitleTranslatorTerm />
          </div>

          <!-- Horizontal resizer -->
          <div class="h-1 bg-gray-700 hover:bg-cyan-500 cursor-row-resize transition-colors duration-200"
            @mousedown="startHorizontalResize"></div>

          <!-- Bottom section - Timeline -->
          <div class="flex-1 flex items-center justify-center">
            <TimelineEditor />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

// Reactive variables for panel sizes (sử dụng percentage của container)
const topHeightPercent = ref(60) // 60% của container height
const leftWidthPercent = ref(50) // 30% của container width

// Computed values for actual pixel sizes
const topHeight = computed(() => Math.floor(containerHeight.value * topHeightPercent.value / 100))
const leftWidth = computed(() => Math.floor(containerWidth.value * leftWidthPercent.value / 100))

// Resize state
const isResizing = ref(false)
const resizeType = ref(null) // 'horizontal' or 'vertical'
const startY = ref(0)
const startX = ref(0)
const startTopHeightPercent = ref(0)
const startLeftWidthPercent = ref(0)

// Container dimensions
const containerHeight = ref(600) // Sẽ được cập nhật từ DOM
const containerWidth = ref(400) // Sẽ được cập nhật từ DOM

// Initialize container size
onMounted(() => {
  nextTick(() => {
    updateContainerSize()
    // Update again after a short delay to ensure DOM is fully rendered
    setTimeout(updateContainerSize, 100)
  })
  window.addEventListener('resize', updateContainerSize)

  // Watch for tab changes and update container size
  // const observer = new MutationObserver(() => {
  //   setTimeout(updateContainerSize, 50)
  // })

  // const tabContainer = document.querySelector('.ant-tabs-content-holder')
  // if (tabContainer) {
  //   observer.observe(tabContainer, {
  //     attributes: true,
  //     childList: true,
  //     subtree: true
  //   })
  // }
})

onUnmounted(() => {
  // window.removeEventListener('resize', updateContainerSize)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

const updateContainerSize = () => {
  let newHeight = 600
  let newWidth = 1000

  // Try multiple selectors to find the container
  const selectors = [
    '.ant-tabs-tabpane-active',
    '.ant-tabs-content-holder',
    '.ant-tabs-content',
    'main'
  ]

  for (const selector of selectors) {
    const element = document.querySelector(selector)
    if (element && element.clientHeight > 0 && element.clientWidth > 0) {
      newHeight = element.clientHeight
      newWidth = element.clientWidth
      break
    }
  }

  // Fallback to window size if no container found
  if (newHeight <= 100 || newWidth <= 100) {
    newHeight = window.innerHeight - 120 // Account for header + tab bar
    newWidth = window.innerWidth - 32 // Account for padding
  }

  containerHeight.value = Math.max(1000, newHeight)
  containerWidth.value = Math.max(600, newWidth)

  // Ensure percentages are within reasonable bounds
  if (topHeightPercent.value > 80) {
    topHeightPercent.value = 60
  }
  if (topHeightPercent.value < 20) {
    topHeightPercent.value = 20
  }
  if (leftWidthPercent.value > 80) {
    leftWidthPercent.value = 40
  }
  if (leftWidthPercent.value < 20) {
    leftWidthPercent.value = 20
  }
}

// Start horizontal resize (top/bottom split)
const startHorizontalResize = (e) => {
  e.preventDefault()
  isResizing.value = true
  resizeType.value = 'horizontal'
  startY.value = e.clientY
  startTopHeightPercent.value = topHeightPercent.value

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'row-resize'
  document.body.style.userSelect = 'none'
}

// Start vertical resize (left/right split)
const startVerticalResize = (e) => {
  e.preventDefault()
  isResizing.value = true
  resizeType.value = 'vertical'
  startX.value = e.clientX
  startLeftWidthPercent.value = leftWidthPercent.value

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

const handleMouseMove = (e) => {
  if (!isResizing.value) return

  if (resizeType.value === 'horizontal') {
    const deltaY = e.clientY - startY.value
    const deltaPercent = (deltaY / containerHeight.value) * 100
    const newHeightPercent = startTopHeightPercent.value + deltaPercent

    // Constrain height between 20% and 80% of container height
    topHeightPercent.value = Math.max(20, Math.min(80, newHeightPercent))
  } else if (resizeType.value === 'vertical') {
    const deltaX = e.clientX - startX.value
    const deltaPercent = (deltaX / containerWidth.value) * 100
    const newWidthPercent = startLeftWidthPercent.value + deltaPercent

    // Constrain width between 20% and 80% of container width
    leftWidthPercent.value = Math.max(20, Math.min(80, newWidthPercent))
  }
}

const handleMouseUp = () => {
  isResizing.value = false
  resizeType.value = null

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}
</script>

<style scoped>
/* Custom background color để match với dark theme */
.bg-gray-750 {
  background-color: #374151;
}

/* Smooth transitions when not resizing */
.transition-all {
  transition: all 0.2s ease;
}

/* Prevent text selection during resize */
.select-none {
  user-select: none;
}

/* Custom scrollbar for panels if needed */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1F1F1F;
}

::-webkit-scrollbar-thumb {
  background: #666666;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888888;
}
</style>